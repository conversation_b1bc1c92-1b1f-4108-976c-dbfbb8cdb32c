import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { ShareIcon, UserIcon, CalendarIcon, EyeIcon } from '@heroicons/react/24/outline';

import { sharingApi } from '../utils/api';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const SharedByMePage: React.FC = () => {
  const { user } = useAuth();

  // Fetch videos shared by current user
  const {
    data: sharedVideos,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['videosSharedByMe'],
    queryFn: () => sharingApi.getVideosSharedByMe(),
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'view':
        return <EyeIcon className="h-4 w-4" />;
      case 'download':
        return <ShareIcon className="h-4 w-4" />;
      case 'comment':
        return <UserIcon className="h-4 w-4" />;
      default:
        return <EyeIcon className="h-4 w-4" />;
    }
  };

  const getPermissionText = (permission: string) => {
    switch (permission) {
      case 'view':
        return 'View only';
      case 'download':
        return 'View & Download';
      case 'comment':
        return 'Full access';
      default:
        return permission;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <ErrorMessage 
        message="Failed to load shared videos" 
        onRetry={() => refetch()}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Videos Shared by {user?.full_name || 'Me'}
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          Manage videos you've shared with other users
        </p>
      </div>

      {/* Shared Videos List */}
      {!sharedVideos || sharedVideos.length === 0 ? (
        <div className="text-center py-12">
          <ShareIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No shared videos</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            You haven't shared any videos yet. Share videos from the video detail page.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {sharedVideos.map((sharedVideo: any) => (
            <div
              key={sharedVideo.video.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
            >
              {/* Video Info */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {sharedVideo.video.title || 'Untitled Video'}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {sharedVideo.video.original_filename}
                  </p>
                  <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    Uploaded {formatDate(sharedVideo.video.created_at)}
                  </div>
                </div>
                <div className="text-right">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    Shared with {sharedVideo.shared_with.length} user{sharedVideo.shared_with.length !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>

              {/* Shared With List */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Shared with:
                </h4>
                {sharedVideo.shared_with.map((share: any) => (
                  <div
                    key={share.user_id}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <UserIcon className="h-8 w-8 text-gray-400" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {share.full_name || share.email}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {share.email}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
                        {getPermissionIcon(share.permissions)}
                        <span>{getPermissionText(share.permissions)}</span>
                      </div>
                      
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Shared {formatDate(share.shared_at)}
                      </div>
                      
                      {share.expires_at && (
                        <div className="text-xs text-orange-600 dark:text-orange-400">
                          Expires {formatDate(share.expires_at)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Actions */}
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => window.location.href = `/video/${sharedVideo.video.id}`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
                >
                  View Video
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SharedByMePage;
