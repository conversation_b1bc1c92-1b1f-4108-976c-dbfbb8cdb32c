import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { UserGroupIcon, UserIcon, CalendarIcon, EyeIcon, PlayIcon } from '@heroicons/react/24/outline';

import { sharingApi } from '../utils/api';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const SharedWithMePage: React.FC = () => {
  const { user } = useAuth();

  // Fetch videos shared with current user
  const {
    data: sharedVideos,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['videosSharedWithMe'],
    queryFn: () => sharingApi.getVideosSharedWithMe(),
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'view':
        return <EyeIcon className="h-4 w-4" />;
      case 'download':
        return <PlayIcon className="h-4 w-4" />;
      case 'comment':
        return <UserIcon className="h-4 w-4" />;
      default:
        return <EyeIcon className="h-4 w-4" />;
    }
  };

  const getPermissionText = (permission: string) => {
    switch (permission) {
      case 'view':
        return 'View only';
      case 'download':
        return 'View & Download';
      case 'comment':
        return 'Full access';
      default:
        return permission;
    }
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <ErrorMessage 
        message="Failed to load shared videos" 
        onRetry={() => refetch()}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Videos Shared with {user?.full_name || 'Me'}
        </h1>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          Videos that other users have shared with you
        </p>
      </div>

      {/* Shared Videos List */}
      {!sharedVideos || sharedVideos.length === 0 ? (
        <div className="text-center py-12">
          <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No shared videos</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            No one has shared any videos with you yet.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sharedVideos.map((sharedVideo: any) => (
            <div
              key={`${sharedVideo.video.id}-${sharedVideo.owner.user_id}`}
              className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden hover:shadow-lg transition-shadow duration-200"
            >
              {/* Video Thumbnail */}
              <div className="aspect-video bg-gray-200 dark:bg-gray-700 relative">
                {sharedVideo.video.thumbnail_path ? (
                  <img
                    src={`/api/videos/${sharedVideo.video.id}/thumbnail`}
                    alt={sharedVideo.video.title || 'Video thumbnail'}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <PlayIcon className="h-12 w-12 text-gray-400" />
                  </div>
                )}
                
                {/* Duration overlay */}
                {sharedVideo.video.duration && (
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    {formatDuration(sharedVideo.video.duration)}
                  </div>
                )}
                
                {/* Permission badge */}
                <div className="absolute top-2 left-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {getPermissionIcon(sharedVideo.permissions)}
                    <span className="ml-1">{getPermissionText(sharedVideo.permissions)}</span>
                  </span>
                </div>
              </div>

              {/* Video Info */}
              <div className="p-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
                  {sharedVideo.video.title || 'Untitled Video'}
                </h3>
                
                <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <UserIcon className="h-4 w-4 mr-2" />
                    <span>Shared by {sharedVideo.owner.full_name || sharedVideo.owner.email}</span>
                  </div>
                  
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    <span>Shared {formatDate(sharedVideo.shared_at)}</span>
                  </div>
                  
                  {sharedVideo.expires_at && (
                    <div className="flex items-center text-orange-600 dark:text-orange-400">
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      <span>Expires {formatDate(sharedVideo.expires_at)}</span>
                    </div>
                  )}
                </div>

                {/* Video metadata */}
                <div className="mt-3 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>{sharedVideo.video.original_filename}</span>
                  {sharedVideo.video.processed && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      Processed
                    </span>
                  )}
                </div>

                {/* Actions */}
                <div className="mt-4">
                  <button
                    onClick={() => window.location.href = `/videos/${sharedVideo.video.id}`}
                    className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    <PlayIcon className="h-4 w-4 mr-2" />
                    View Video
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SharedWithMePage;
