import React, { useState, useEffect } from 'react';
import { XMarkIcon, UserPlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

import { sharingApi } from '../utils/api';
import { VideoShareRequest, VideoShareResponse } from '../types';
import LoadingSpinner from './LoadingSpinner';

interface VideoSharingModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoId: number;
  videoTitle: string;
}

const VideoSharingModal: React.FC<VideoSharingModalProps> = ({
  isOpen,
  onClose,
  videoId,
  videoTitle
}) => {
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [permissions, setPermissions] = useState<'view' | 'download' | 'comment'>('view');
  const [expiresAt, setExpiresAt] = useState('');

  // Fetch current shares for this video
  const { data: currentShares, refetch: refetchShares } = useQuery<VideoShareResponse[]>({
    queryKey: ['videoShares', videoId],
    queryFn: () => sharingApi.getVideoShares(videoId),
    enabled: isOpen
  });

  // Search users
  const { data: searchResults, isLoading: isSearching } = useQuery({
    queryKey: ['userSearch', searchQuery],
    queryFn: () => sharingApi.searchUsers(searchQuery),
    enabled: searchQuery.length >= 2
  });

  // Share video mutation
  const shareMutation = useMutation({
    mutationFn: (shareRequest: VideoShareRequest) => 
      sharingApi.shareVideo(videoId, shareRequest),
    onSuccess: () => {
      toast.success('Video shared successfully!');
      setSelectedUser(null);
      setSearchQuery('');
      setExpiresAt('');
      refetchShares();
      queryClient.invalidateQueries({ queryKey: ['videosSharedByMe'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to share video');
    }
  });

  // Unshare video mutation
  const unshareMutation = useMutation({
    mutationFn: (sharedWithUserId: string) => 
      sharingApi.unshareVideo(videoId, sharedWithUserId),
    onSuccess: () => {
      toast.success('Video unshared successfully!');
      refetchShares();
      queryClient.invalidateQueries({ queryKey: ['videosSharedByMe'] });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to unshare video');
    }
  });

  const handleShare = () => {
    if (!selectedUser) return;

    const shareRequest: VideoShareRequest = {
      shared_with_email: selectedUser.email,
      permissions,
      expires_at: expiresAt || undefined
    };

    shareMutation.mutate(shareRequest);
  };

  const handleUnshare = (sharedWithUserId: string) => {
    if (window.confirm('Are you sure you want to stop sharing this video?')) {
      unshareMutation.mutate(sharedWithUserId);
    }
  };

  const handleUserSelect = (user: any) => {
    setSelectedUser(user);
    setSearchQuery(user.email);
  };

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setSelectedUser(null);
      setPermissions('view');
      setExpiresAt('');
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Share Video
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Video info */}
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Sharing: <span className="font-medium text-gray-900 dark:text-white">{videoTitle}</span>
          </div>

          {/* User search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Share with user
            </label>
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by email or name..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* Search results */}
            {isSearching && (
              <div className="mt-2 p-2 text-center">
                <LoadingSpinner size="sm" />
              </div>
            )}

            {searchResults && searchResults.length > 0 && !selectedUser && (
              <div className="mt-2 border border-gray-200 dark:border-gray-600 rounded-md max-h-32 overflow-y-auto">
                {searchResults.map((user) => (
                  <button
                    key={user.id}
                    onClick={() => handleUserSelect(user)}
                    className="w-full text-left px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                  >
                    <div className="font-medium text-gray-900 dark:text-white">{user.full_name}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Permissions */}
          {selectedUser && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Permissions
                </label>
                <select
                  value={permissions}
                  onChange={(e) => setPermissions(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="view">View only</option>
                  <option value="download">View and download</option>
                  <option value="comment">View, download, and comment</option>
                </select>
              </div>

              {/* Expiration */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Expires (optional)
                </label>
                <input
                  type="datetime-local"
                  value={expiresAt}
                  onChange={(e) => setExpiresAt(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              {/* Share button */}
              <button
                onClick={handleShare}
                disabled={shareMutation.isPending}
                className="w-full flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {shareMutation.isPending ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <>
                    <UserPlusIcon className="h-4 w-4 mr-2" />
                    Share Video
                  </>
                )}
              </button>
            </>
          )}

          {/* Current shares */}
          {currentShares && currentShares.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Currently shared with:
              </h4>
              <div className="space-y-2">
                {currentShares.map((share) => (
                  <div
                    key={share.id}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md"
                  >
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {share.shared_with_email}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {share.permissions} • Shared {new Date(share.shared_at).toLocaleDateString()}
                      </div>
                    </div>
                    <button
                      onClick={() => handleUnshare(share.shared_with_email)}
                      disabled={unshareMutation.isPending}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoSharingModal;
