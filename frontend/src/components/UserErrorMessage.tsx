import React from 'react';
import { ExclamationTriangleIcon, LockClosedIcon, UserIcon } from '@heroicons/react/24/outline';

interface UserErrorMessageProps {
  error: any;
  onRetry?: () => void;
  fallbackMessage?: string;
}

const UserErrorMessage: React.FC<UserErrorMessageProps> = ({ 
  error, 
  onRetry, 
  fallbackMessage = "Something went wrong" 
}) => {
  const getErrorInfo = () => {
    const status = error?.response?.status;
    const message = error?.response?.data?.detail || error?.message;

    switch (status) {
      case 401:
        return {
          icon: LockClosedIcon,
          title: "Authentication Required",
          message: "Please log in to access this content.",
          color: "text-yellow-600",
          bgColor: "bg-yellow-50",
          borderColor: "border-yellow-200",
          showRetry: false
        };
      
      case 403:
        return {
          icon: UserIcon,
          title: "Access Denied",
          message: "You don't have permission to access this content.",
          color: "text-red-600",
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
          showRetry: false
        };
      
      case 404:
        return {
          icon: ExclamationTriangleIcon,
          title: "Not Found",
          message: "The content you're looking for doesn't exist or you don't have access to it.",
          color: "text-gray-600",
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200",
          showRetry: true
        };
      
      case 500:
        return {
          icon: ExclamationTriangleIcon,
          title: "Server Error",
          message: "Something went wrong on our end. Please try again later.",
          color: "text-red-600",
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
          showRetry: true
        };
      
      default:
        return {
          icon: ExclamationTriangleIcon,
          title: "Error",
          message: message || fallbackMessage,
          color: "text-gray-600",
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200",
          showRetry: true
        };
    }
  };

  const errorInfo = getErrorInfo();
  const Icon = errorInfo.icon;

  return (
    <div className={`rounded-lg border ${errorInfo.borderColor} ${errorInfo.bgColor} p-6`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <Icon className={`h-6 w-6 ${errorInfo.color}`} />
        </div>
        <div className="ml-3 flex-1">
          <h3 className={`text-sm font-medium ${errorInfo.color}`}>
            {errorInfo.title}
          </h3>
          <div className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            <p>{errorInfo.message}</p>
          </div>
          {errorInfo.showRetry && onRetry && (
            <div className="mt-4">
              <button
                onClick={onRetry}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Try Again
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserErrorMessage;
