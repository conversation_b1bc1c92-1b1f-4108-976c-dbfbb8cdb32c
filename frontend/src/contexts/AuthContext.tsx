import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, UserCreate, UserLogin, AuthContextType } from '../types';
import { authApi } from '../utils/api';
import toast from 'react-hot-toast';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Listen for auth logout events from API interceptor
  useEffect(() => {
    const handleAuthLogout = () => {
      clearAuthData();
    };

    window.addEventListener('auth:logout', handleAuthLogout);
    return () => window.removeEventListener('auth:logout', handleAuthLogout);
  }, []);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedToken = localStorage.getItem('access_token');
        const storedRefreshToken = localStorage.getItem('refresh_token');
        
        if (storedToken) {
          setToken(storedToken);
          
          try {
            // Verify token and get user info
            const userData = await authApi.getCurrentUser(storedToken);
            setUser(userData);
          } catch (error) {
            // Token might be expired, try to refresh
            if (storedRefreshToken) {
              try {
                const tokenData = await authApi.refreshToken(storedRefreshToken);
                setToken(tokenData.access_token);
                localStorage.setItem('access_token', tokenData.access_token);
                localStorage.setItem('refresh_token', tokenData.refresh_token);
                
                // Get user info with new token
                const userData = await authApi.getCurrentUser(tokenData.access_token);
                setUser(userData);
              } catch (refreshError) {
                // Refresh failed, clear tokens
                clearAuthData();
              }
            } else {
              clearAuthData();
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        clearAuthData();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const clearAuthData = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  };

  const login = async (email: string, password: string): Promise<void> => {
    try {
      setIsLoading(true);
      const tokenData = await authApi.login({ email, password });
      
      // Store tokens
      setToken(tokenData.access_token);
      localStorage.setItem('access_token', tokenData.access_token);
      localStorage.setItem('refresh_token', tokenData.refresh_token);
      
      // Get user info
      const userData = await authApi.getCurrentUser(tokenData.access_token);
      setUser(userData);
      
      toast.success('Successfully logged in!');
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Login failed';
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: UserCreate): Promise<void> => {
    try {
      setIsLoading(true);
      await authApi.register(userData);
      
      // Auto-login after registration
      await login(userData.email, userData.password);
      
      toast.success('Account created successfully!');
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Registration failed';
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    clearAuthData();
    toast.success('Successfully logged out');
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    register,
    logout,
    isAuthenticated: !!user && !!token,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
