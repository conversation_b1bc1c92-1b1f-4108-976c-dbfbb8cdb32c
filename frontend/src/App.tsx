import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';

import { ThemeProvider } from './contexts/ThemeContext';
import { VideoSelectionProvider } from './contexts/VideoSelectionContext';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import HomePage from './pages/HomePage';
import VideoDetailPage from './pages/VideoDetailPage';
import AnalyticsPage from './pages/AnalyticsPage';
import UploadPage from './pages/UploadPage';
import DownloadPage from './pages/DownloadPage';
import RecipesPage from './pages/RecipesPage';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes garbage collection
      refetchOnMount: true, // Always refetch on mount to ensure fresh data
    },
    mutations: {
      retry: 1,
    },
  },
});

const App: React.FC = () => {
  return (
    <ThemeProvider>
      <AuthProvider>
        <VideoSelectionProvider>
          <QueryClientProvider client={queryClient}>
            <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
              <div className="App">
                <ProtectedRoute>
                  <Layout>
                    <Routes>
                      <Route path="/" element={<HomePage />} />
                      <Route path="/video/:id" element={<VideoDetailPage />} />
                      <Route path="/recetas" element={<RecipesPage />} />
                      <Route path="/analytics" element={<AnalyticsPage />} />
                      <Route path="/upload" element={<UploadPage />} />
                      <Route path="/download" element={<DownloadPage />} />
                    </Routes>
                  </Layout>
                </ProtectedRoute>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                className: 'dark:bg-gray-800 dark:text-white',
                style: {
                  background: 'var(--toast-bg)',
                  color: 'var(--toast-color)',
                },
                success: {
                  duration: 3000,
                  iconTheme: {
                    primary: '#4ade80',
                    secondary: '#fff',
                  },
                },
                error: {
                  duration: 5000,
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                },
              }}
            />
          </div>
            </Router>
          </QueryClientProvider>
        </VideoSelectionProvider>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
