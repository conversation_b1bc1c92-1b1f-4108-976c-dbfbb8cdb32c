<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TagTok Login Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        .success-btn { background: #28a745; }
        .success-btn:hover { background: #218838; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .login-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 TagTok Login Flow Test</h1>
        
        <div class="test-section info">
            <h3>📋 Test Instructions</h3>
            <p>This page will test the complete login flow for TagTok's multi-user system.</p>
            <ol>
                <li>Clear localStorage to ensure clean state</li>
                <li>Test API endpoints directly</li>
                <li>Test login with the form below</li>
                <li>Verify authentication works</li>
                <li>Test protected endpoints</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧹 Clean State</h3>
            <button onclick="clearAll()" class="danger">Clear All Data</button>
            <button onclick="checkState()">Check Current State</button>
            <div id="state-result"></div>
        </div>

        <div class="test-section">
            <h3>🌐 API Health Check</h3>
            <button onclick="testApiHealth()">Test API Health</button>
            <div id="health-result"></div>
        </div>

        <div class="test-section">
            <h3>🔑 Direct Login Test</h3>
            <div class="login-form">
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" value="TestPassword123!">
                </div>
                <button onclick="testLogin()" class="success-btn">Test Login</button>
            </div>
            <div id="login-result"></div>
        </div>

        <div class="test-section">
            <h3>🛡️ Protected Endpoint Test</h3>
            <button onclick="testProtectedEndpoint()">Test Videos Endpoint</button>
            <button onclick="testTagsEndpoint()">Test Tags Endpoint</button>
            <div id="protected-result"></div>
        </div>

        <div class="test-section">
            <h3>🚀 Frontend Test</h3>
            <button onclick="openFrontend()">Open TagTok Frontend</button>
            <button onclick="openFrontendNewTab()">Open in New Tab</button>
            <div id="frontend-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8790/api';
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML = `<div class="${className}"><pre>${message}</pre></div>`;
        }

        function clearAll() {
            localStorage.clear();
            sessionStorage.clear();
            log('state-result', '✅ All storage cleared!', 'success');
        }

        function checkState() {
            const state = {
                localStorage_keys: Object.keys(localStorage),
                sessionStorage_keys: Object.keys(sessionStorage),
                access_token: localStorage.getItem('access_token'),
                refresh_token: localStorage.getItem('refresh_token')
            };
            
            const message = `LocalStorage keys: ${JSON.stringify(state.localStorage_keys)}
SessionStorage keys: ${JSON.stringify(state.sessionStorage_keys)}
Access token: ${state.access_token ? 'Present (' + state.access_token.substring(0, 20) + '...)' : 'Not found'}
Refresh token: ${state.refresh_token ? 'Present (' + state.refresh_token.substring(0, 20) + '...)' : 'Not found'}`;
            
            log('state-result', message, state.access_token ? 'error' : 'success');
        }

        async function testApiHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.text();
                log('health-result', `✅ API Health Check Successful!
Status: ${response.status}
Response: ${data}
API Base URL: ${API_BASE}`, 'success');
            } catch (error) {
                log('health-result', `❌ API Health Check Failed:
Error: ${error.message}
API Base URL: ${API_BASE}`, 'error');
            }
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                log('login-result', '🔄 Attempting login...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Store tokens like the frontend would
                    localStorage.setItem('access_token', data.access_token);
                    if (data.refresh_token) {
                        localStorage.setItem('refresh_token', data.refresh_token);
                    }
                    
                    log('login-result', `✅ Login Successful!
Status: ${response.status}
Token Type: ${data.token_type}
Access Token: ${data.access_token.substring(0, 30)}...
Refresh Token: ${data.refresh_token ? data.refresh_token.substring(0, 30) + '...' : 'Not provided'}

Tokens stored in localStorage for testing.`, 'success');
                } else {
                    log('login-result', `❌ Login Failed:
Status: ${response.status}
Error: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                log('login-result', `❌ Login Request Failed:
Error: ${error.message}
Stack: ${error.stack}`, 'error');
            }
        }

        async function testProtectedEndpoint() {
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                log('protected-result', '❌ No access token found. Please login first.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/videos/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log('protected-result', `✅ Videos Endpoint Success!
Status: ${response.status}
Videos Count: ${data.length}
Response: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    log('protected-result', `❌ Videos Endpoint Failed:
Status: ${response.status}
Error: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                log('protected-result', `❌ Videos Request Failed:
Error: ${error.message}`, 'error');
            }
        }

        async function testTagsEndpoint() {
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                log('protected-result', '❌ No access token found. Please login first.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/tags/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log('protected-result', `✅ Tags Endpoint Success!
Status: ${response.status}
Tags Count: ${data.length}
Response: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    log('protected-result', `❌ Tags Endpoint Failed:
Status: ${response.status}
Error: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                log('protected-result', `❌ Tags Request Failed:
Error: ${error.message}`, 'error');
            }
        }

        function openFrontend() {
            window.location.href = 'http://localhost:3001';
        }

        function openFrontendNewTab() {
            window.open('http://localhost:3001', '_blank');
            log('frontend-result', '🚀 Frontend opened in new tab. Check if login works!', 'info');
        }

        // Auto-check state on page load
        window.onload = function() {
            checkState();
            testApiHealth();
        };
    </script>
</body>
</html>
