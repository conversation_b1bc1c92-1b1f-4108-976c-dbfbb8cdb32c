<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TagTok Frontend Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 TagTok Frontend Authentication Test</h1>
        
        <div class="test-section info">
            <h3>📋 Test Instructions</h3>
            <p>This page will help you test the TagTok frontend authentication system.</p>
            <ol>
                <li>Check current localStorage state</li>
                <li>Clear any existing tokens</li>
                <li>Test API endpoints</li>
                <li>Verify frontend behavior</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔍 Current localStorage State</h3>
            <button onclick="checkLocalStorage()">Check localStorage</button>
            <button onclick="clearTokens()" class="danger">Clear All Tokens</button>
            <div id="localStorage-result"></div>
        </div>

        <div class="test-section">
            <h3>🌐 API Connection Test</h3>
            <button onclick="testApiConnection()">Test API Connection</button>
            <div id="api-test-result"></div>
        </div>

        <div class="test-section">
            <h3>🔑 Authentication Test</h3>
            <button onclick="testLogin()">Test Login</button>
            <button onclick="testProtectedEndpoint()">Test Protected Endpoint</button>
            <div id="auth-test-result"></div>
        </div>

        <div class="test-section">
            <h3>🚀 Frontend Navigation</h3>
            <button onclick="goToFrontend()">Go to TagTok Frontend</button>
            <button onclick="reloadFrontend()">Reload Frontend (New Tab)</button>
            <div id="frontend-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8790/api';
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            element.innerHTML = `<div class="${className}"><pre>${message}</pre></div>`;
        }

        function checkLocalStorage() {
            const tokens = {
                access_token: localStorage.getItem('access_token'),
                refresh_token: localStorage.getItem('refresh_token')
            };
            
            const message = `Access Token: ${tokens.access_token ? 'Present (' + tokens.access_token.substring(0, 20) + '...)' : 'Not found'}
Refresh Token: ${tokens.refresh_token ? 'Present (' + tokens.refresh_token.substring(0, 20) + '...)' : 'Not found'}

All localStorage keys: ${JSON.stringify(Object.keys(localStorage), null, 2)}`;
            
            log('localStorage-result', message, tokens.access_token ? 'error' : 'success');
        }

        function clearTokens() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.clear(); // Clear everything to be sure
            log('localStorage-result', '✅ All tokens and localStorage cleared!', 'success');
        }

        async function testApiConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.text();
                log('api-test-result', `✅ API Connection successful!
Status: ${response.status}
Response: ${data}`, 'success');
            } catch (error) {
                log('api-test-result', `❌ API Connection failed:
Error: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'TestPassword123!'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log('auth-test-result', `✅ Login successful!
Access Token: ${data.access_token.substring(0, 30)}...
Token Type: ${data.token_type}`, 'success');
                } else {
                    log('auth-test-result', `❌ Login failed:
Status: ${response.status}
Error: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                log('auth-test-result', `❌ Login request failed:
Error: ${error.message}`, 'error');
            }
        }

        async function testProtectedEndpoint() {
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                log('auth-test-result', '❌ No access token found. Please login first or clear tokens and reload frontend.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/videos/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log('auth-test-result', `✅ Protected endpoint access successful!
Status: ${response.status}
Videos: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    log('auth-test-result', `❌ Protected endpoint access failed:
Status: ${response.status}
Error: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                log('auth-test-result', `❌ Protected endpoint request failed:
Error: ${error.message}`, 'error');
            }
        }

        function goToFrontend() {
            window.location.href = 'http://localhost:3001';
        }

        function reloadFrontend() {
            window.open('http://localhost:3001', '_blank');
            log('frontend-result', '🚀 Frontend opened in new tab. Check if login screen appears!', 'info');
        }

        // Auto-check localStorage on page load
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
