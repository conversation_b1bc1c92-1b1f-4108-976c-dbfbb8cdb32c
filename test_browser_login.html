<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TagTok Browser Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        .success-btn { background: #28a745; }
        .success-btn:hover { background: #218838; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .login-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .console-error { color: #f48771; }
        .console-warn { color: #dcdcaa; }
        .console-info { color: #9cdcfe; }
        .console-log { color: #d4d4d4; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 TagTok Browser Login Test</h1>
        <p>This page simulates the exact browser login flow to test CORS and authentication.</p>
    </div>

    <div class="container">
        <h2>🧹 Step 1: Clean Browser State</h2>
        <div class="test-section warning">
            <p><strong>Important:</strong> Clear all stored data to ensure a clean test environment.</p>
            <button onclick="clearAllData()" class="danger">Clear All Browser Data</button>
            <button onclick="checkBrowserState()">Check Current State</button>
            <div id="browser-state"></div>
        </div>
    </div>

    <div class="container">
        <h2>🌐 Step 2: Test API Connection</h2>
        <div class="test-section">
            <button onclick="testApiConnection()">Test API Health</button>
            <button onclick="testCorsHeaders()">Test CORS Headers</button>
            <div id="api-connection"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔑 Step 3: Simulate Frontend Login</h2>
        <div class="test-section">
            <div class="login-form">
                <h3>Login Credentials</h3>
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" value="TestPassword123!">
                </div>
                <button onclick="simulateLogin()" class="success-btn">Simulate Frontend Login</button>
                <button onclick="testProtectedEndpoints()">Test Protected Endpoints</button>
            </div>
            <div id="login-result"></div>
        </div>
    </div>

    <div class="container">
        <h2>🖥️ Step 4: Browser Console Monitor</h2>
        <div class="test-section">
            <p>Monitor for any console errors during the login process:</p>
            <button onclick="startConsoleMonitoring()">Start Console Monitoring</button>
            <button onclick="clearConsoleLog()">Clear Console Log</button>
            <div id="console-monitor" class="console-output"></div>
        </div>
    </div>

    <div class="container">
        <h2>🚀 Step 5: Open TagTok Frontend</h2>
        <div class="test-section">
            <p>After clearing data and verifying the API works, test the actual frontend:</p>
            <button onclick="openTagTokFrontend()">Open TagTok Frontend</button>
            <button onclick="openTagTokNewTab()">Open in New Tab</button>
            <div id="frontend-test"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8790/api';
        let consoleMonitoring = false;
        let originalConsole = {};
        
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            element.innerHTML = `<div class="${className}"><pre>${message}</pre></div>`;
        }

        function clearAllData() {
            // Clear all storage
            localStorage.clear();
            sessionStorage.clear();
            
            // Clear cookies (best effort)
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            log('browser-state', '✅ All browser data cleared!\n- localStorage: cleared\n- sessionStorage: cleared\n- cookies: cleared', 'success');
        }

        function checkBrowserState() {
            const state = {
                localStorage: Object.keys(localStorage),
                sessionStorage: Object.keys(sessionStorage),
                cookies: document.cookie
            };
            
            const message = `LocalStorage keys: ${JSON.stringify(state.localStorage)}
SessionStorage keys: ${JSON.stringify(state.sessionStorage)}
Cookies: ${state.cookies || 'none'}

Access token: ${localStorage.getItem('access_token') ? 'Present' : 'Not found'}
Refresh token: ${localStorage.getItem('refresh_token') ? 'Present' : 'Not found'}`;
            
            const hasData = state.localStorage.length > 0 || state.sessionStorage.length > 0 || state.cookies;
            log('browser-state', message, hasData ? 'warning' : 'success');
        }

        async function testApiConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.text();
                log('api-connection', `✅ API Health Check Successful!
Status: ${response.status}
Response: ${data}
API Base URL: ${API_BASE}`, 'success');
            } catch (error) {
                log('api-connection', `❌ API Health Check Failed:
Error: ${error.message}
API Base URL: ${API_BASE}`, 'error');
            }
        }

        async function testCorsHeaders() {
            try {
                // Test preflight request
                const preflightResponse = await fetch(`${API_BASE}/auth/login`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'http://localhost:3001',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type,Authorization'
                    }
                });
                
                const corsHeaders = {};
                for (const [key, value] of preflightResponse.headers.entries()) {
                    if (key.toLowerCase().includes('access-control')) {
                        corsHeaders[key] = value;
                    }
                }
                
                log('api-connection', `✅ CORS Preflight Successful!
Status: ${preflightResponse.status}
CORS Headers: ${JSON.stringify(corsHeaders, null, 2)}`, 'success');
            } catch (error) {
                log('api-connection', `❌ CORS Test Failed:
Error: ${error.message}`, 'error');
            }
        }

        async function simulateLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                log('login-result', '🔄 Attempting login (simulating frontend behavior)...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'http://localhost:3001'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Store tokens like the frontend would
                    localStorage.setItem('access_token', data.access_token);
                    if (data.refresh_token) {
                        localStorage.setItem('refresh_token', data.refresh_token);
                    }
                    
                    log('login-result', `✅ Login Successful! (Exactly like frontend would work)
Status: ${response.status}
Token Type: ${data.token_type}
Access Token: ${data.access_token.substring(0, 30)}...
Refresh Token: ${data.refresh_token ? data.refresh_token.substring(0, 30) + '...' : 'Not provided'}

✅ Tokens stored in localStorage (simulating frontend behavior)
✅ No CORS errors detected!`, 'success');
                } else {
                    log('login-result', `❌ Login Failed:
Status: ${response.status}
Error: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                log('login-result', `❌ Login Request Failed (CORS or Network Error):
Error: ${error.message}
Stack: ${error.stack}

This indicates a CORS or network connectivity issue.`, 'error');
            }
        }

        async function testProtectedEndpoints() {
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                log('login-result', '❌ No access token found. Please login first.', 'error');
                return;
            }

            try {
                // Test videos endpoint
                const videosResponse = await fetch(`${API_BASE}/videos/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Origin': 'http://localhost:3001'
                    }
                });
                
                const videosData = await videosResponse.json();
                
                // Test tags endpoint
                const tagsResponse = await fetch(`${API_BASE}/tags/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Origin': 'http://localhost:3001'
                    }
                });
                
                const tagsData = await tagsResponse.json();
                
                if (videosResponse.ok && tagsResponse.ok) {
                    log('login-result', `✅ Protected Endpoints Working!
Videos Endpoint: ${videosResponse.status} (${videosData.length} videos)
Tags Endpoint: ${tagsResponse.status} (${tagsData.length} tags)

✅ Authentication is working correctly!
✅ No CORS errors on protected endpoints!`, 'success');
                } else {
                    log('login-result', `❌ Protected Endpoints Failed:
Videos: ${videosResponse.status}
Tags: ${tagsResponse.status}`, 'error');
                }
            } catch (error) {
                log('login-result', `❌ Protected Endpoints Error:
Error: ${error.message}`, 'error');
            }
        }

        function startConsoleMonitoring() {
            if (consoleMonitoring) return;
            
            consoleMonitoring = true;
            const consoleElement = document.getElementById('console-monitor');
            
            // Store original console methods
            originalConsole = {
                log: console.log,
                error: console.error,
                warn: console.warn,
                info: console.info
            };
            
            function addToConsoleLog(type, args) {
                const timestamp = new Date().toLocaleTimeString();
                const message = Array.from(args).map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                
                const logEntry = document.createElement('div');
                logEntry.className = `console-${type}`;
                logEntry.textContent = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
                consoleElement.appendChild(logEntry);
                consoleElement.scrollTop = consoleElement.scrollHeight;
            }
            
            // Override console methods
            console.log = function(...args) {
                originalConsole.log.apply(console, args);
                addToConsoleLog('log', args);
            };
            
            console.error = function(...args) {
                originalConsole.error.apply(console, args);
                addToConsoleLog('error', args);
            };
            
            console.warn = function(...args) {
                originalConsole.warn.apply(console, args);
                addToConsoleLog('warn', args);
            };
            
            console.info = function(...args) {
                originalConsole.info.apply(console, args);
                addToConsoleLog('info', args);
            };
            
            consoleElement.innerHTML = '<div class="console-info">[Console monitoring started]</div>';
        }

        function clearConsoleLog() {
            document.getElementById('console-monitor').innerHTML = '';
        }

        function openTagTokFrontend() {
            window.location.href = 'http://localhost:3001';
        }

        function openTagTokNewTab() {
            window.open('http://localhost:3001', '_blank');
            log('frontend-test', '🚀 TagTok frontend opened in new tab.\n\nExpected behavior:\n1. Should show login screen (if localStorage was cleared)\n2. Login should work without CORS errors\n3. Should redirect to dashboard after successful login', 'info');
        }

        // Auto-check state on page load
        window.onload = function() {
            checkBrowserState();
            testApiConnection();
        };
    </script>
</body>
</html>
