# 👥 TagTok Test Users for Multi-User System

## 🧪 Available Test Users

### Test User 1 (Original)
- **Email:** `<EMAIL>`
- **Password:** `TestPassword123!`
- **Full Name:** Test User
- **Status:** ✅ Active (has 1 video, 27 tags)

### Test User 2 (Alice)
- **Email:** `<EMAIL>`
- **Password:** `AlicePassword123!`
- **Full Name:** <PERSON> Johnson
- **Status:** ✅ <PERSON> (new user, 0 videos, 27 tags)

### Test User 3 (Bob)
- **Email:** `<EMAIL>`
- **Password:** `BobPassword123!`
- **Full Name:** <PERSON>
- **Status:** ✅ Active (new user, 0 videos, 27 tags)

## 🔧 How to Use Test Users

### 1. **Login Testing**
Use the test page at `file:///Users/<USER>/github/tests/tagTok/test_browser_login.html`:
- Click the "Use Test User 1", "Use Alice", or "Use Bob" buttons
- Credentials will be auto-filled
- Test login functionality

### 2. **Frontend Testing**
1. Open `http://localhost:3001`
2. Clear localStorage if needed
3. Login with any of the test credentials
4. Test the multi-user dashboard

### 3. **Video Sharing Testing**
1. **Upload a video as User 1:**
   - Login as `<EMAIL>`
   - Upload a video
   - Share it with `<EMAIL>`

2. **Check shared videos as Alice:**
   - Logout and login as `<EMAIL>`
   - Check "Shared with Me" section
   - Should see the video shared by Test User 1

3. **Test sharing permissions:**
   - Try different sharing permissions (view, download, comment)
   - Test expiration dates
   - Test sharing with multiple users

### 4. **API Testing**
Use the verification scripts:
```bash
# Test all users
python3 test_all_users.py

# Test CORS and login flow
python3 verify_cors_fix.py
```

## 🎯 Testing Scenarios

### Scenario 1: Basic Multi-User Flow
1. Login as Test User 1
2. Upload a cooking video
3. Add tags and recipe information
4. Share with Alice (`<EMAIL>`)
5. Login as Alice
6. View shared video in "Shared with Me"
7. Test video playback and recipe viewing

### Scenario 2: Bidirectional Sharing
1. Alice uploads and shares with Bob
2. Bob uploads and shares with Alice
3. Both users should see videos in "Shared with Me"
4. Test that users only see their own videos in main library

### Scenario 3: Permission Testing
1. Share video with "view" permission
2. Recipient should not be able to download
3. Share video with "download" permission
4. Recipient should be able to download
5. Test comment permissions

### Scenario 4: User Isolation
1. Login as different users
2. Verify each user only sees their own:
   - Videos in main library
   - Tags they created
   - Analytics for their videos
3. Verify shared videos appear separately

## 🚀 Quick Test Commands

### Create Additional Test User
```bash
curl -X POST "http://localhost:8790/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "NewPassword123!",
    "full_name": "New Test User"
  }'
```

### Test Login
```bash
curl -X POST "http://localhost:8790/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "AlicePassword123!"
  }'
```

### Test Protected Endpoint
```bash
# Replace TOKEN with actual token from login
curl -H "Authorization: Bearer TOKEN" \
  "http://localhost:8790/api/videos/"
```

## 📋 Expected Behavior

### ✅ What Should Work
- All three users can login successfully
- Each user has isolated video libraries
- Sharing functionality works between users
- "Shared with Me" and "Shared by Me" sections work
- User-specific analytics and tags
- CORS headers work correctly
- No authentication errors in browser console

### ❌ What Should NOT Happen
- Users seeing other users' private videos
- Cross-user data leakage
- CORS errors in browser console
- Authentication failures
- Rate limiting issues (except during rapid testing)

## 🔧 Troubleshooting

### Rate Limiting Issues
If you get rate limit errors during testing:
1. Wait 60 seconds between login attempts
2. Or temporarily disable rate limiting in `.env`:
   ```
   RATE_LIMIT_ENABLED=false
   ```
3. Restart backend: `docker-compose restart backend`

### CORS Issues
If you see CORS errors:
1. Check that nginx is running: `docker-compose ps`
2. Verify frontend is using `http://localhost:8790/api`
3. Check browser console for specific errors

### Authentication Issues
If login fails:
1. Verify user exists in database
2. Check password requirements (8+ chars, uppercase, lowercase, digit)
3. Check API logs: `docker-compose logs backend`

## 🎉 Success Criteria

The multi-user system is working correctly when:
1. ✅ All test users can login
2. ✅ Users have isolated data
3. ✅ Video sharing works between users
4. ✅ No CORS errors in browser
5. ✅ Frontend shows correct user-specific data
6. ✅ Sharing permissions are enforced
7. ✅ "Shared with Me" / "Shared by Me" sections work
