#!/usr/bin/env python3
"""
Database migration script to update TagTok database schema for multi-user support
"""

import os
import sqlite3
import sys
from pathlib import Path

def backup_database(db_path):
    """Create a backup of the existing database"""
    backup_path = f"{db_path}.backup"
    
    if os.path.exists(db_path):
        print(f"📦 Creating backup: {backup_path}")
        
        # Copy the database file
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ Backup created successfully")
        return backup_path
    else:
        print(f"ℹ️  No existing database found at {db_path}")
        return None

def migrate_database(db_path):
    """Migrate the database schema to support multi-user features"""
    print(f"🔄 Migrating database: {db_path}")
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if users table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users';")
        users_table_exists = cursor.fetchone() is not None
        
        if not users_table_exists:
            print("📋 Creating users table...")
            cursor.execute("""
                CREATE TABLE users (
                    id TEXT PRIMARY KEY,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    email_verified BOOLEAN DEFAULT 0,
                    reset_token TEXT,
                    reset_token_expires DATETIME,
                    is_active BOOLEAN DEFAULT 1
                );
            """)
            
            cursor.execute("CREATE INDEX ix_users_id ON users (id);")
            cursor.execute("CREATE INDEX ix_users_email ON users (email);")
            print("✅ Users table created")
        else:
            print("ℹ️  Users table already exists")
        
        # Check if tags table has user_id column
        cursor.execute("PRAGMA table_info(tags);")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'user_id' not in columns:
            print("📋 Adding user_id column to tags table...")
            cursor.execute("ALTER TABLE tags ADD COLUMN user_id TEXT;")
            cursor.execute("CREATE INDEX ix_tags_user_id ON tags (user_id);")
            print("✅ user_id column added to tags table")
        else:
            print("ℹ️  tags.user_id column already exists")
        
        # Check if videos table has user_id column
        cursor.execute("PRAGMA table_info(videos);")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'user_id' not in columns:
            print("📋 Adding user_id column to videos table...")
            cursor.execute("ALTER TABLE videos ADD COLUMN user_id TEXT;")
            cursor.execute("CREATE INDEX ix_videos_user_id ON videos (user_id);")
            print("✅ user_id column added to videos table")
        else:
            print("ℹ️  videos.user_id column already exists")
        
        # Check if video_shares table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='video_shares';")
        video_shares_exists = cursor.fetchone() is not None
        
        if not video_shares_exists:
            print("📋 Creating video_shares table...")
            cursor.execute("""
                CREATE TABLE video_shares (
                    id TEXT PRIMARY KEY,
                    video_id INTEGER NOT NULL,
                    shared_by_user_id TEXT NOT NULL,
                    shared_with_email TEXT NOT NULL,
                    permissions TEXT DEFAULT 'view',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (video_id) REFERENCES videos (id),
                    FOREIGN KEY (shared_by_user_id) REFERENCES users (id)
                );
            """)
            
            cursor.execute("CREATE INDEX ix_video_shares_id ON video_shares (id);")
            cursor.execute("CREATE INDEX ix_video_shares_video_id ON video_shares (video_id);")
            cursor.execute("CREATE INDEX ix_video_shares_shared_by_user_id ON video_shares (shared_by_user_id);")
            cursor.execute("CREATE INDEX ix_video_shares_shared_with_email ON video_shares (shared_with_email);")
            print("✅ video_shares table created")
        else:
            print("ℹ️  video_shares table already exists")
        
        # Commit the changes
        conn.commit()
        print("✅ Database migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

def main():
    print("🚀 TagTok Database Migration - Multi-User Support")
    print("=" * 60)
    
    # Determine database path
    db_path = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")
    
    if db_path.startswith("sqlite:///"):
        db_path = db_path.replace("sqlite:///", "")
    
    # Make sure the directory exists
    db_dir = os.path.dirname(db_path)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        print(f"📁 Created directory: {db_dir}")
    
    print(f"🗄️  Database path: {db_path}")
    
    # Create backup
    backup_path = backup_database(db_path)
    
    # Perform migration
    success = migrate_database(db_path)
    
    if success:
        print()
        print("🎉 Migration completed successfully!")
        print()
        print("📋 Summary:")
        print("• Users table created/verified")
        print("• user_id columns added to tags and videos tables")
        print("• video_shares table created for sharing functionality")
        print("• All necessary indexes created")
        
        if backup_path:
            print(f"• Backup saved at: {backup_path}")
        
        print()
        print("🔧 Next Steps:")
        print("1. Restart the backend container: docker-compose restart backend")
        print("2. The multi-user system should now work properly")
        print("3. You can test with the existing test user credentials")
        
    else:
        print()
        print("❌ Migration failed!")
        if backup_path and os.path.exists(backup_path):
            print(f"💾 You can restore from backup: {backup_path}")
        sys.exit(1)

if __name__ == "__main__":
    main()
