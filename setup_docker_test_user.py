#!/usr/bin/env python3
"""
Setup script to create test user in Docker environment
"""

import sys
import os
import time
import requests
import json

def wait_for_backend(url="http://localhost:8080", max_attempts=30):
    """Wait for backend to be ready"""
    print("🔄 Waiting for backend to be ready...")
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Backend is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print(f"   Attempt {attempt + 1}/{max_attempts} - Backend not ready yet...")
        time.sleep(2)
    
    print("❌ Backend failed to start within timeout")
    return False

def create_test_user(base_url="http://localhost:8080"):
    """Create the test user"""
    print("👤 Creating test user...")
    
    user_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "full_name": "Test User"
    }
    
    try:
        # Try to register the user
        response = requests.post(f"{base_url}/auth/register", json=user_data, timeout=10)
        
        if response.status_code in [200, 201]:
            print("✅ Test user created successfully!")
            user_info = response.json()
            print(f"   Email: {user_info['email']}")
            print(f"   Name: {user_info['full_name']}")
            return True
        elif response.status_code == 400 and "already registered" in response.text.lower():
            print("ℹ️  Test user already exists")
            return True
        else:
            print(f"❌ Failed to create test user: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error creating test user: {e}")
        return False

def test_login(base_url="http://localhost:8080"):
    """Test login with the test user"""
    print("🔑 Testing login...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }
    
    try:
        response = requests.post(f"{base_url}/auth/login", json=login_data, timeout=10)
        
        if response.status_code == 200:
            print("✅ Login test successful!")
            token_data = response.json()
            print(f"   Access token received: {token_data['access_token'][:20]}...")
            return True
        else:
            print(f"❌ Login test failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error testing login: {e}")
        return False

def check_frontend(url="http://localhost:3001"):
    """Check if frontend is accessible"""
    print("🌐 Checking frontend...")
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            print("✅ Frontend is accessible!")
            return True
        else:
            print(f"❌ Frontend returned status: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error accessing frontend: {e}")
        return False

def main():
    print("🚀 TagTok Docker Setup - Multi-User Test Environment")
    print("=" * 60)
    
    # Check if we should use custom URLs
    backend_url = os.getenv("BACKEND_URL", "http://localhost:8080")
    frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3001")
    
    print(f"Backend URL: {backend_url}")
    print(f"Frontend URL: {frontend_url}")
    print()
    
    # Wait for backend
    if not wait_for_backend(backend_url):
        sys.exit(1)
    
    # Create test user
    if not create_test_user(backend_url):
        sys.exit(1)
    
    # Test login
    if not test_login(backend_url):
        sys.exit(1)
    
    # Check frontend
    if not check_frontend(frontend_url):
        print("⚠️  Frontend check failed, but continuing...")
    
    print()
    print("🎉 Setup completed successfully!")
    print()
    print("📋 Test User Credentials:")
    print("   Email: <EMAIL>")
    print("   Password: TestPassword123!")
    print()
    print("🌐 Access URLs:")
    print(f"   Frontend: {frontend_url}")
    print(f"   Backend API: {backend_url}")
    print(f"   API Health: {backend_url}/health")
    print()
    print("🔧 Next Steps:")
    print("1. Open your browser and go to:", frontend_url)
    print("2. You should see the login page")
    print("3. Use the test credentials above to log in")
    print("4. You'll be redirected to your personal video dashboard")
    print()
    print("✨ Multi-user features to test:")
    print("• User registration and login")
    print("• Personal video library (isolated per user)")
    print("• Video sharing with other users")
    print("• User-specific analytics")
    print("• Rate limiting and security features")

if __name__ == "__main__":
    main()
