# TagTok Multi-User Deployment Guide

This guide covers deploying the multi-user TagTok application with authentication, data isolation, and video sharing capabilities.

## 🏗️ System Requirements

### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 50GB (more for video storage)
- **OS**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+ / Windows 10+

### Recommended Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 100GB+ SSD
- **Network**: 100Mbps+ for video uploads

### Software Dependencies
- **Python**: 3.9+
- **Node.js**: 16+
- **PostgreSQL**: 13+ (or SQLite for development)
- **Redis**: 6+ (optional, for production rate limiting)
- **FFmpeg**: Latest (for video processing)

## 🚀 Quick Start (Development)

### 1. Clone and Setup
```bash
git clone <repository-url>
cd tagTok

# Backend setup
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Frontend setup
cd ../frontend
npm install
```

### 2. Environment Configuration
Create `backend/.env`:
```env
# Database
DATABASE_URL=sqlite:///./tagtok.db

# JWT Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# File Storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=100000000  # 100MB

# Security
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
RATE_LIMIT_ENABLED=true
```

### 3. Database Migration
```bash
cd backend
python migrate_to_multiuser.py --default-user-email <EMAIL>
```

### 4. Create Test User
```bash
python create_test_user.py
```

### 5. Start Services
```bash
# Backend (Terminal 1)
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Frontend (Terminal 2)
cd frontend
npm start
```

## 🏭 Production Deployment

### Option 1: Docker Deployment (Recommended)

#### 1. Create Docker Compose Configuration
Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: tagtok
      POSTGRES_USER: tagtok
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      DATABASE_URL: postgresql://tagtok:${POSTGRES_PASSWORD}@postgres:5432/tagtok
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY}
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 2. Create Production Environment File
Create `.env.prod`:
```env
POSTGRES_PASSWORD=your-secure-postgres-password
SECRET_KEY=your-super-secure-secret-key-64-characters-long
SMTP_HOST=your-smtp-host
SMTP_USERNAME=your-smtp-username
SMTP_PASSWORD=your-smtp-password
```

#### 3. Deploy
```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### Option 2: Manual Server Deployment

#### 1. Server Setup (Ubuntu 20.04+)
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3.9 python3.9-venv python3-pip nodejs npm postgresql postgresql-contrib redis-server nginx ffmpeg

# Create application user
sudo useradd -m -s /bin/bash tagtok
sudo usermod -aG sudo tagtok
```

#### 2. Database Setup
```bash
# Configure PostgreSQL
sudo -u postgres createuser tagtok
sudo -u postgres createdb tagtok -O tagtok
sudo -u postgres psql -c "ALTER USER tagtok PASSWORD 'your-secure-password';"
```

#### 3. Application Deployment
```bash
# Switch to app user
sudo su - tagtok

# Clone repository
git clone <repository-url> /home/<USER>/tagtok
cd /home/<USER>/tagtok

# Backend setup
cd backend
python3.9 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Create production environment
cp .env.example .env
# Edit .env with production values

# Run migrations
python migrate_to_multiuser.py

# Frontend setup
cd ../frontend
npm install
npm run build
```

#### 4. Process Management (systemd)
Create `/etc/systemd/system/tagtok-backend.service`:
```ini
[Unit]
Description=TagTok Backend
After=network.target postgresql.service

[Service]
Type=simple
User=tagtok
WorkingDirectory=/home/<USER>/tagtok/backend
Environment=PATH=/home/<USER>/tagtok/backend/venv/bin
ExecStart=/home/<USER>/tagtok/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
```

#### 5. Nginx Configuration
Create `/etc/nginx/sites-available/tagtok`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # Frontend
    location / {
        root /home/<USER>/tagtok/frontend/build;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api/ {
        proxy_pass http://127.0.0.1:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # File uploads (increase limits)
    client_max_body_size 100M;
    proxy_read_timeout 300s;
    proxy_connect_timeout 75s;
}
```

#### 6. Enable and Start Services
```bash
# Enable services
sudo systemctl enable tagtok-backend
sudo systemctl enable nginx
sudo systemctl enable postgresql
sudo systemctl enable redis

# Start services
sudo systemctl start tagtok-backend
sudo systemctl start nginx

# Enable site
sudo ln -s /etc/nginx/sites-available/tagtok /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 Security Configuration

### 1. SSL/TLS Setup (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. Firewall Configuration
```bash
# Configure UFW
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 3. Database Security
```bash
# Secure PostgreSQL
sudo -u postgres psql
\password postgres  # Set strong password
\q

# Edit pg_hba.conf for stricter access
sudo nano /etc/postgresql/13/main/pg_hba.conf
```

## 📊 Monitoring and Maintenance

### 1. Log Management
```bash
# View application logs
sudo journalctl -u tagtok-backend -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 2. Database Backup
```bash
# Create backup script
cat > /home/<USER>/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U tagtok tagtok > /home/<USER>/backups/tagtok_$DATE.sql
find /home/<USER>/backups -name "*.sql" -mtime +7 -delete
EOF

chmod +x /home/<USER>/backup.sh

# Schedule daily backups
crontab -e
# Add: 0 2 * * * /home/<USER>/backup.sh
```

### 3. Health Checks
```bash
# Check service status
sudo systemctl status tagtok-backend
sudo systemctl status nginx
sudo systemctl status postgresql

# Check application health
curl http://localhost:8000/health
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connection
sudo -u postgres psql -c "SELECT version();"

# Reset password
sudo -u postgres psql -c "ALTER USER tagtok PASSWORD 'new-password';"
```

#### 2. File Upload Issues
```bash
# Check disk space
df -h

# Check upload directory permissions
ls -la /home/<USER>/tagtok/backend/uploads/

# Fix permissions
sudo chown -R tagtok:tagtok /home/<USER>/tagtok/backend/uploads/
sudo chmod -R 755 /home/<USER>/tagtok/backend/uploads/
```

#### 3. Frontend Build Issues
```bash
# Clear cache and rebuild
cd /home/<USER>/tagtok/frontend
rm -rf node_modules package-lock.json
npm install
npm run build
```

## 📈 Performance Optimization

### 1. Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_videos_user_id ON videos(user_id);
CREATE INDEX idx_videos_created_at ON videos(created_at);
CREATE INDEX idx_shared_videos_shared_with ON shared_videos(shared_with_user_id);
CREATE INDEX idx_tags_user_id ON tags(user_id);
```

### 2. Nginx Caching
Add to nginx configuration:
```nginx
# Static file caching
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# API response caching
location /api/analytics/ {
    proxy_cache_valid 200 5m;
    proxy_cache_key $request_uri;
}
```

### 3. Redis Configuration
For production rate limiting, configure Redis in `backend/.env`:
```env
REDIS_URL=redis://localhost:6379
RATE_LIMIT_STORAGE=redis
```

## 🔄 Updates and Maintenance

### 1. Application Updates
```bash
# Backup before update
/home/<USER>/backup.sh

# Pull latest code
cd /home/<USER>/tagtok
git pull origin main

# Update backend
cd backend
source venv/bin/activate
pip install -r requirements.txt

# Update frontend
cd ../frontend
npm install
npm run build

# Restart services
sudo systemctl restart tagtok-backend
sudo systemctl reload nginx
```

### 2. Database Migrations
```bash
# Run any new migrations
cd /home/<USER>/tagtok/backend
source venv/bin/activate
python migrate_to_multiuser.py  # If needed
```

This deployment guide provides comprehensive instructions for both development and production environments. Choose the deployment method that best fits your infrastructure and requirements.
