#!/usr/bin/env python3
"""
Verification script to test CORS fix for TagTok multi-user system
"""

import requests
import json
import sys

def test_cors_preflight():
    """Test CORS preflight request"""
    print("🔍 Testing CORS preflight request...")
    
    try:
        response = requests.options(
            "http://localhost:8790/api/auth/login",
            headers={
                "Origin": "http://localhost:3001",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type,Authorization"
            }
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   CORS Headers:")
        for header, value in response.headers.items():
            if 'access-control' in header.lower():
                print(f"     {header}: {value}")
        
        if response.status_code == 204:
            print("✅ CORS preflight successful!")
            return True
        else:
            print("❌ CORS preflight failed!")
            return False
            
    except Exception as e:
        print(f"❌ CORS preflight error: {e}")
        return False

def test_login_with_cors():
    """Test login with CORS headers"""
    print("\n🔑 Testing login with CORS...")
    
    try:
        response = requests.post(
            "http://localhost:8790/api/auth/login",
            headers={
                "Content-Type": "application/json",
                "Origin": "http://localhost:3001"
            },
            json={
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   CORS Headers:")
        for header, value in response.headers.items():
            if 'access-control' in header.lower():
                print(f"     {header}: {value}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"   Access token: {data['access_token'][:30]}...")
            print(f"   Token type: {data['token_type']}")
            return data['access_token']
        else:
            print("❌ Login failed!")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_protected_endpoint(token):
    """Test protected endpoint with token"""
    print("\n🛡️ Testing protected endpoint...")
    
    try:
        response = requests.get(
            "http://localhost:8790/api/videos/",
            headers={
                "Authorization": f"Bearer {token}",
                "Origin": "http://localhost:3001"
            }
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   CORS Headers:")
        for header, value in response.headers.items():
            if 'access-control' in header.lower():
                print(f"     {header}: {value}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Protected endpoint successful!")
            print(f"   Videos count: {len(data)}")
            return True
        else:
            print("❌ Protected endpoint failed!")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Protected endpoint error: {e}")
        return False

def test_health_endpoint():
    """Test health endpoint"""
    print("\n🏥 Testing health endpoint...")
    
    try:
        response = requests.get(
            "http://localhost:8790/api/health",
            headers={"Origin": "http://localhost:3001"}
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Health endpoint successful!")
            return True
        else:
            print("❌ Health endpoint failed!")
            return False
            
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def main():
    print("🚀 TagTok CORS Fix Verification")
    print("=" * 50)
    
    # Test health endpoint first
    health_ok = test_health_endpoint()
    
    # Test CORS preflight
    preflight_ok = test_cors_preflight()
    
    # Test login with CORS
    token = test_login_with_cors()
    login_ok = token is not None
    
    # Test protected endpoint if login succeeded
    protected_ok = False
    if token:
        protected_ok = test_protected_endpoint(token)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Health Endpoint: {'✅ PASS' if health_ok else '❌ FAIL'}")
    print(f"   CORS Preflight: {'✅ PASS' if preflight_ok else '❌ FAIL'}")
    print(f"   Login with CORS: {'✅ PASS' if login_ok else '❌ FAIL'}")
    print(f"   Protected Endpoint: {'✅ PASS' if protected_ok else '❌ FAIL'}")
    
    all_passed = health_ok and preflight_ok and login_ok and protected_ok
    
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 CORS fix successful! Frontend should now work correctly.")
        print("\n📋 Next Steps:")
        print("1. Open http://localhost:3001 in your browser")
        print("2. You should see the login screen")
        print("3. Login with: <EMAIL> / TestPassword123!")
        print("4. You should be redirected to the dashboard without CORS errors")
    else:
        print("\n🔧 Some tests failed. Please check the error messages above.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
