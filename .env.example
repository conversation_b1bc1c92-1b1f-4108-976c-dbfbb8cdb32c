# tagTok Environment Configuration Example
# Copy this file to .env and modify as needed

# Database Configuration
DATABASE_URL=sqlite:///db/tagTok.db

# Supabase Configuration (for multi-user support)
SUPABASE_URL=https://ppuzdsgahfoyhrwrgdvr.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key

# JWT Authentication Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=15
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Directory Paths
VIDEOS_DIR=/app/videos
TRANSCRIPTS_DIR=/app/transcripts

# Port Configuration - Centralized port management
# External ports (what users connect to)
NGINX_PORT=8790
BACKEND_EXTERNAL_PORT=8080
FRONTEND_EXTERNAL_PORT=3001
OLLAMA_EXTERNAL_PORT=11435

# Internal service ports (inside Docker containers)
BACKEND_INTERNAL_PORT=8000
FRONTEND_INTERNAL_PORT=80
OLLAMA_INTERNAL_PORT=11434

# Service URLs for application code
BACKEND_URL=http://backend:8000
OLLAMA_URL=http://ollama:11434
FRONTEND_URL=http://frontend:80

# API Configuration for frontend
REACT_APP_API_URL=http://localhost:8080

# AI Model Configuration
WHISPER_MODEL_SIZE=base

# Development Configuration
PYTHONPATH=/app
CHOKIDAR_USEPOLLING=true

# Docker Configuration
COMPOSE_PROJECT_NAME=tagtok

# Development overrides (uncomment for local development)
# BACKEND_URL=http://localhost:8080
# OLLAMA_URL=http://localhost:11435
# REACT_APP_API_URL=http://localhost:8080
