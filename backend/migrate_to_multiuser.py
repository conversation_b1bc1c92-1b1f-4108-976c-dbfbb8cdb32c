#!/usr/bin/env python3
"""
Database migration script to convert tagTok from single-user to multi-user system.

This script:
1. Creates the new user table and shared_videos table
2. Adds user_id columns to existing tables
3. Creates a default user for existing data
4. Updates all existing records to belong to the default user
5. Sets up proper foreign key constraints

Usage:
    python migrate_to_multiuser.py [--default-user-email <EMAIL>]
"""

import sys
import os
import argparse
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import Base, User, Video, Tag, Recipe, SharedVideo
from utils.auth import get_password_hash
from config import Config

def check_table_exists(engine, table_name):
    """Check if a table exists in the database"""
    inspector = inspect(engine)
    return table_name in inspector.get_table_names()

def check_column_exists(engine, table_name, column_name):
    """Check if a column exists in a table"""
    inspector = inspect(engine)
    if not check_table_exists(engine, table_name):
        return False
    columns = [col['name'] for col in inspector.get_columns(table_name)]
    return column_name in columns

def migrate_database(default_user_email="<EMAIL>", default_user_password="AdminPassword123!"):
    """Perform the database migration"""
    
    print("🚀 Starting tagTok multi-user migration...")
    
    # Create database engine
    engine = create_engine(Config.DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Step 1: Check if migration is needed
        print("\n📋 Step 1: Checking current database state...")
        
        if check_table_exists(engine, 'users'):
            print("✅ Users table already exists. Migration may have already been run.")
            user_count = session.execute(text("SELECT COUNT(*) FROM users")).scalar()
            print(f"   Found {user_count} users in the database.")
            
            if user_count > 0:
                print("⚠️  Migration appears to have been completed already.")
                response = input("Do you want to continue anyway? (y/N): ")
                if response.lower() != 'y':
                    print("Migration cancelled.")
                    return
        
        # Step 2: Create new tables
        print("\n🏗️  Step 2: Creating new database tables...")
        Base.metadata.create_all(engine)
        print("✅ Database tables created/updated successfully.")
        
        # Step 3: Add user_id columns to existing tables if they don't exist
        print("\n🔧 Step 3: Adding user_id columns to existing tables...")
        
        # Add user_id to videos table
        if not check_column_exists(engine, 'videos', 'user_id'):
            print("   Adding user_id column to videos table...")
            session.execute(text("ALTER TABLE videos ADD COLUMN user_id VARCHAR"))
            session.commit()
            print("   ✅ Added user_id to videos table")
        else:
            print("   ✅ user_id column already exists in videos table")
        
        # Add user_id to tags table
        if not check_column_exists(engine, 'tags', 'user_id'):
            print("   Adding user_id column to tags table...")
            session.execute(text("ALTER TABLE tags ADD COLUMN user_id VARCHAR"))
            session.commit()
            print("   ✅ Added user_id to tags table")
        else:
            print("   ✅ user_id column already exists in tags table")
        
        # Step 4: Create default user
        print("\n👤 Step 4: Creating default user...")
        
        # Check if default user already exists
        existing_user = session.query(User).filter(User.email == default_user_email).first()
        if existing_user:
            print(f"   ✅ Default user {default_user_email} already exists")
            default_user = existing_user
        else:
            default_user = User(
                email=default_user_email,
                password_hash=get_password_hash(default_user_password),
                full_name="Default Admin User",
                email_verified=True,
                is_active=True
            )
            session.add(default_user)
            session.commit()
            session.refresh(default_user)
            print(f"   ✅ Created default user: {default_user_email}")
            print(f"   🔑 Default password: {default_user_password}")
        
        # Step 5: Update existing videos to belong to default user
        print("\n📹 Step 5: Updating existing videos...")
        
        videos_without_user = session.execute(
            text("SELECT COUNT(*) FROM videos WHERE user_id IS NULL")
        ).scalar()
        
        if videos_without_user > 0:
            session.execute(
                text("UPDATE videos SET user_id = :user_id WHERE user_id IS NULL"),
                {"user_id": default_user.id}
            )
            session.commit()
            print(f"   ✅ Updated {videos_without_user} videos to belong to default user")
        else:
            print("   ✅ All videos already have user assignments")
        
        # Step 6: Update existing tags to be global (user_id = NULL) or assign to default user
        print("\n🏷️  Step 6: Updating existing tags...")
        
        tags_without_user = session.execute(
            text("SELECT COUNT(*) FROM tags WHERE user_id IS NULL")
        ).scalar()
        
        if tags_without_user > 0:
            # Keep existing tags as global tags (user_id = NULL)
            print(f"   ✅ Keeping {tags_without_user} existing tags as global tags")
        else:
            print("   ✅ All tags already have user assignments")
        
        # Step 7: Verify migration
        print("\n✅ Step 7: Verifying migration...")
        
        total_users = session.query(User).count()
        total_videos = session.query(Video).count()
        videos_with_users = session.query(Video).filter(Video.user_id.isnot(None)).count()
        total_tags = session.query(Tag).count()
        
        print(f"   📊 Migration Summary:")
        print(f"      - Total users: {total_users}")
        print(f"      - Total videos: {total_videos}")
        print(f"      - Videos with user assignment: {videos_with_users}")
        print(f"      - Total tags: {total_tags}")
        
        if videos_with_users == total_videos:
            print("   ✅ All videos have been assigned to users")
        else:
            print(f"   ⚠️  {total_videos - videos_with_users} videos still need user assignment")
        
        print("\n🎉 Migration completed successfully!")
        print(f"\n📝 Next steps:")
        print(f"   1. Update your environment variables to use the new multi-user system")
        print(f"   2. Test login with: {default_user_email} / {default_user_password}")
        print(f"   3. Create additional user accounts as needed")
        print(f"   4. Consider changing the default user password")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        session.rollback()
        raise
    finally:
        session.close()

def main():
    parser = argparse.ArgumentParser(description='Migrate tagTok to multi-user system')
    parser.add_argument(
        '--default-user-email', 
        default='<EMAIL>',
        help='Email for the default admin user (default: <EMAIL>)'
    )
    parser.add_argument(
        '--default-user-password',
        default='AdminPassword123!',
        help='Password for the default admin user (default: AdminPassword123!)'
    )
    
    args = parser.parse_args()
    
    print("🔄 tagTok Multi-User Migration Tool")
    print("=" * 50)
    print(f"Database URL: {Config.DATABASE_URL}")
    print(f"Default user email: {args.default_user_email}")
    print("=" * 50)
    
    response = input("\nProceed with migration? (y/N): ")
    if response.lower() != 'y':
        print("Migration cancelled.")
        return
    
    migrate_database(args.default_user_email, args.default_user_password)

if __name__ == "__main__":
    main()
