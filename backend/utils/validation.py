import re
import html
from typing import Optional, List, Any
from fastapi import HTTPException
import bleach

# Allowed HTML tags for rich text content (if needed)
ALLOWED_TAGS = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li']
ALLOWED_ATTRIBUTES = {}

def sanitize_html(text: str) -> str:
    """Sanitize HTML content to prevent XSS attacks"""
    if not text:
        return ""
    
    # Use bleach to clean HTML
    cleaned = bleach.clean(text, tags=ALLOWED_TAGS, attributes=ALLOWED_ATTRIBUTES, strip=True)
    return cleaned

def sanitize_string(text: str, max_length: Optional[int] = None, allow_html: bool = False) -> str:
    """Sanitize string input"""
    if not text:
        return ""
    
    # Remove null bytes and control characters
    text = text.replace('\x00', '').replace('\r', '').strip()
    
    # Limit length if specified
    if max_length and len(text) > max_length:
        raise HTTPException(status_code=400, detail=f"Text too long. Maximum {max_length} characters allowed.")
    
    if allow_html:
        return sanitize_html(text)
    else:
        # Escape HTML entities for plain text
        return html.escape(text)

def validate_email(email: str) -> str:
    """Validate and sanitize email address"""
    if not email:
        raise HTTPException(status_code=400, detail="Email is required")
    
    email = email.strip().lower()
    
    # Basic email regex validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        raise HTTPException(status_code=400, detail="Invalid email format")
    
    if len(email) > 254:  # RFC 5321 limit
        raise HTTPException(status_code=400, detail="Email address too long")
    
    return email

def validate_password(password: str) -> str:
    """Validate password strength"""
    if not password:
        raise HTTPException(status_code=400, detail="Password is required")
    
    if len(password) < 8:
        raise HTTPException(status_code=400, detail="Password must be at least 8 characters long")
    
    if len(password) > 128:
        raise HTTPException(status_code=400, detail="Password too long")
    
    # Check for at least one uppercase, lowercase, digit, and special character
    if not re.search(r'[A-Z]', password):
        raise HTTPException(status_code=400, detail="Password must contain at least one uppercase letter")
    
    if not re.search(r'[a-z]', password):
        raise HTTPException(status_code=400, detail="Password must contain at least one lowercase letter")
    
    if not re.search(r'\d', password):
        raise HTTPException(status_code=400, detail="Password must contain at least one digit")
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        raise HTTPException(status_code=400, detail="Password must contain at least one special character")
    
    return password

def validate_filename(filename: str) -> str:
    """Validate and sanitize filename"""
    if not filename:
        raise HTTPException(status_code=400, detail="Filename is required")
    
    # Remove path traversal attempts
    filename = filename.replace('..', '').replace('/', '').replace('\\', '')
    
    # Remove null bytes and control characters
    filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', filename)
    
    # Limit length
    if len(filename) > 255:
        raise HTTPException(status_code=400, detail="Filename too long")
    
    # Check for valid characters (alphanumeric, spaces, dots, hyphens, underscores)
    if not re.match(r'^[a-zA-Z0-9\s._-]+$', filename):
        raise HTTPException(status_code=400, detail="Filename contains invalid characters")
    
    return filename.strip()

def validate_tag_name(tag_name: str) -> str:
    """Validate and sanitize tag name"""
    if not tag_name:
        raise HTTPException(status_code=400, detail="Tag name is required")
    
    tag_name = tag_name.strip()
    
    if len(tag_name) > 50:
        raise HTTPException(status_code=400, detail="Tag name too long. Maximum 50 characters allowed.")
    
    # Allow alphanumeric, spaces, hyphens, and underscores
    if not re.match(r'^[a-zA-Z0-9\s_-]+$', tag_name):
        raise HTTPException(status_code=400, detail="Tag name contains invalid characters")
    
    return tag_name

def validate_color_hex(color: str) -> str:
    """Validate hex color code"""
    if not color:
        return "#000000"  # Default color
    
    color = color.strip()
    
    # Ensure it starts with #
    if not color.startswith('#'):
        color = '#' + color
    
    # Validate hex color format
    if not re.match(r'^#[0-9A-Fa-f]{6}$', color):
        raise HTTPException(status_code=400, detail="Invalid color format. Use hex format like #FF0000")
    
    return color.upper()

def validate_search_query(query: str) -> str:
    """Validate and sanitize search query"""
    if not query:
        return ""
    
    query = query.strip()
    
    if len(query) > 200:
        raise HTTPException(status_code=400, detail="Search query too long")
    
    # Remove potentially dangerous characters
    query = re.sub(r'[<>"\']', '', query)
    
    return query

def validate_pagination_params(skip: int, limit: int) -> tuple[int, int]:
    """Validate pagination parameters"""
    if skip < 0:
        raise HTTPException(status_code=400, detail="Skip parameter must be non-negative")
    
    if limit < 1:
        raise HTTPException(status_code=400, detail="Limit parameter must be positive")
    
    if limit > 1000:
        raise HTTPException(status_code=400, detail="Limit parameter too large. Maximum 1000 allowed.")
    
    return skip, limit

def validate_video_title(title: str) -> str:
    """Validate and sanitize video title"""
    if not title:
        return "Untitled Video"
    
    title = sanitize_string(title, max_length=200)
    
    if not title.strip():
        return "Untitled Video"
    
    return title

def validate_user_full_name(name: str) -> str:
    """Validate and sanitize user full name"""
    if not name:
        return ""
    
    name = sanitize_string(name, max_length=100)
    
    # Allow letters, spaces, hyphens, apostrophes
    if not re.match(r"^[a-zA-Z\s\-']+$", name):
        raise HTTPException(status_code=400, detail="Name contains invalid characters")
    
    return name.strip()

def validate_permissions(permissions: str) -> str:
    """Validate sharing permissions"""
    valid_permissions = ['view', 'download', 'comment']
    
    if permissions not in valid_permissions:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid permissions. Must be one of: {', '.join(valid_permissions)}"
        )
    
    return permissions

def validate_language_code(language: str) -> str:
    """Validate language code"""
    if not language:
        return "en"
    
    language = language.lower().strip()
    
    # Basic validation for ISO 639-1 language codes
    if not re.match(r'^[a-z]{2}$', language):
        raise HTTPException(status_code=400, detail="Invalid language code. Use ISO 639-1 format (e.g., 'en', 'es')")
    
    return language

def sanitize_json_field(data: Any) -> Any:
    """Sanitize JSON field data"""
    if isinstance(data, str):
        return sanitize_string(data, max_length=10000)
    elif isinstance(data, list):
        return [sanitize_json_field(item) for item in data[:100]]  # Limit list size
    elif isinstance(data, dict):
        return {k: sanitize_json_field(v) for k, v in list(data.items())[:50]}  # Limit dict size
    else:
        return data

# Common validation patterns
PATTERNS = {
    'uuid': r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
    'slug': r'^[a-z0-9-]+$',
    'username': r'^[a-zA-Z0-9_-]{3,30}$',
    'phone': r'^\+?[1-9]\d{1,14}$',
}

def validate_pattern(value: str, pattern_name: str) -> str:
    """Validate value against a predefined pattern"""
    if pattern_name not in PATTERNS:
        raise ValueError(f"Unknown pattern: {pattern_name}")
    
    if not re.match(PATTERNS[pattern_name], value):
        raise HTTPException(status_code=400, detail=f"Invalid {pattern_name} format")
    
    return value
