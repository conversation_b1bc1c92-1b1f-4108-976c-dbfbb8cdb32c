import logging
import json
from datetime import datetime
from typing import Optional, Dict, Any
from fastapi import Request
import os

# Configure audit logger
audit_logger = logging.getLogger("audit")
audit_logger.setLevel(logging.INFO)

# Create audit log file handler
audit_log_file = os.path.join("logs", "audit.log")
os.makedirs(os.path.dirname(audit_log_file), exist_ok=True)

file_handler = logging.FileHandler(audit_log_file)
file_handler.setLevel(logging.INFO)

# Create formatter for audit logs
audit_formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
file_handler.setFormatter(audit_formatter)

# Add handler to audit logger
if not audit_logger.handlers:
    audit_logger.addHandler(file_handler)

class AuditEvent:
    """Audit event types"""
    # Authentication events
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGOUT = "logout"
    REGISTER = "register"
    PASSWORD_RESET_REQUEST = "password_reset_request"
    PASSWORD_RESET_SUCCESS = "password_reset_success"
    TOKEN_REFRESH = "token_refresh"
    
    # Video events
    VIDEO_UPLOAD = "video_upload"
    VIDEO_DELETE = "video_delete"
    VIDEO_UPDATE = "video_update"
    VIDEO_DOWNLOAD = "video_download"
    VIDEO_VIEW = "video_view"
    VIDEO_REPROCESS = "video_reprocess"
    
    # Sharing events
    VIDEO_SHARE = "video_share"
    VIDEO_UNSHARE = "video_unshare"
    SHARED_VIDEO_ACCESS = "shared_video_access"
    
    # Tag events
    TAG_CREATE = "tag_create"
    TAG_UPDATE = "tag_update"
    TAG_DELETE = "tag_delete"
    
    # Security events
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    INVALID_TOKEN = "invalid_token"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    
    # System events
    API_ERROR = "api_error"
    SYSTEM_ERROR = "system_error"

def get_client_info(request: Request) -> Dict[str, Any]:
    """Extract client information from request"""
    return {
        "ip_address": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "referer": request.headers.get("referer"),
        "x_forwarded_for": request.headers.get("x-forwarded-for"),
        "x_real_ip": request.headers.get("x-real-ip"),
        "request_id": getattr(request.state, 'request_id', None)
    }

def log_audit_event(
    event_type: str,
    user_id: Optional[str] = None,
    user_email: Optional[str] = None,
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    request: Optional[Request] = None,
    success: bool = True,
    error_message: Optional[str] = None
):
    """Log an audit event"""
    
    audit_data = {
        "timestamp": datetime.utcnow().isoformat(),
        "event_type": event_type,
        "success": success,
        "user_id": user_id,
        "user_email": user_email,
        "resource_type": resource_type,
        "resource_id": resource_id,
        "details": details or {},
        "error_message": error_message
    }
    
    # Add client information if request is provided
    if request:
        audit_data["client_info"] = get_client_info(request)
        audit_data["endpoint"] = request.url.path
        audit_data["method"] = request.method
    
    # Log the audit event
    audit_logger.info(json.dumps(audit_data, default=str))

def log_authentication_event(
    event_type: str,
    user_email: str,
    request: Request,
    success: bool = True,
    error_message: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
):
    """Log authentication-related events"""
    log_audit_event(
        event_type=event_type,
        user_email=user_email,
        request=request,
        success=success,
        error_message=error_message,
        details=details
    )

def log_video_event(
    event_type: str,
    user_id: str,
    video_id: Optional[int] = None,
    request: Optional[Request] = None,
    details: Optional[Dict[str, Any]] = None,
    success: bool = True,
    error_message: Optional[str] = None
):
    """Log video-related events"""
    log_audit_event(
        event_type=event_type,
        user_id=user_id,
        resource_type="video",
        resource_id=str(video_id) if video_id else None,
        request=request,
        details=details,
        success=success,
        error_message=error_message
    )

def log_sharing_event(
    event_type: str,
    owner_id: str,
    video_id: int,
    shared_with_email: str,
    request: Optional[Request] = None,
    details: Optional[Dict[str, Any]] = None,
    success: bool = True,
    error_message: Optional[str] = None
):
    """Log sharing-related events"""
    sharing_details = {
        "shared_with_email": shared_with_email,
        **(details or {})
    }
    
    log_audit_event(
        event_type=event_type,
        user_id=owner_id,
        resource_type="video_share",
        resource_id=str(video_id),
        request=request,
        details=sharing_details,
        success=success,
        error_message=error_message
    )

def log_security_event(
    event_type: str,
    request: Request,
    user_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    error_message: Optional[str] = None
):
    """Log security-related events"""
    log_audit_event(
        event_type=event_type,
        user_id=user_id,
        request=request,
        details=details,
        success=False,  # Security events are typically failures
        error_message=error_message
    )

def log_api_error(
    error_type: str,
    request: Request,
    user_id: Optional[str] = None,
    error_message: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
):
    """Log API errors"""
    log_audit_event(
        event_type=AuditEvent.API_ERROR,
        user_id=user_id,
        request=request,
        details={
            "error_type": error_type,
            **(details or {})
        },
        success=False,
        error_message=error_message
    )

# Decorator for automatic audit logging
def audit_log(event_type: str, resource_type: Optional[str] = None):
    """Decorator to automatically log function calls"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                
                # Try to extract user and request info from kwargs
                user_id = None
                request = None
                
                for arg in kwargs.values():
                    if hasattr(arg, 'id') and hasattr(arg, 'email'):  # User object
                        user_id = arg.id
                    elif hasattr(arg, 'client'):  # Request object
                        request = arg
                
                log_audit_event(
                    event_type=event_type,
                    user_id=user_id,
                    resource_type=resource_type,
                    request=request,
                    success=True
                )
                
                return result
                
            except Exception as e:
                # Log the error
                user_id = None
                request = None
                
                for arg in kwargs.values():
                    if hasattr(arg, 'id') and hasattr(arg, 'email'):
                        user_id = arg.id
                    elif hasattr(arg, 'client'):
                        request = arg
                
                log_audit_event(
                    event_type=event_type,
                    user_id=user_id,
                    resource_type=resource_type,
                    request=request,
                    success=False,
                    error_message=str(e)
                )
                
                raise
        
        return wrapper
    return decorator

# Audit log analysis functions
def get_audit_logs(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    event_type: Optional[str] = None,
    user_id: Optional[str] = None,
    limit: int = 100
) -> List[Dict[str, Any]]:
    """Retrieve audit logs with filtering"""
    logs = []
    
    try:
        with open(audit_log_file, 'r') as f:
            for line in f:
                try:
                    log_entry = json.loads(line.strip())
                    
                    # Apply filters
                    if start_date and datetime.fromisoformat(log_entry['timestamp']) < start_date:
                        continue
                    if end_date and datetime.fromisoformat(log_entry['timestamp']) > end_date:
                        continue
                    if event_type and log_entry.get('event_type') != event_type:
                        continue
                    if user_id and log_entry.get('user_id') != user_id:
                        continue
                    
                    logs.append(log_entry)
                    
                    if len(logs) >= limit:
                        break
                        
                except json.JSONDecodeError:
                    continue
                    
    except FileNotFoundError:
        pass
    
    return logs[-limit:]  # Return most recent logs

def get_security_events(hours: int = 24) -> List[Dict[str, Any]]:
    """Get security events from the last N hours"""
    from datetime import timedelta
    
    start_date = datetime.utcnow() - timedelta(hours=hours)
    
    security_event_types = [
        AuditEvent.RATE_LIMIT_EXCEEDED,
        AuditEvent.UNAUTHORIZED_ACCESS,
        AuditEvent.INVALID_TOKEN,
        AuditEvent.SUSPICIOUS_ACTIVITY,
        AuditEvent.LOGIN_FAILED
    ]
    
    all_security_events = []
    for event_type in security_event_types:
        events = get_audit_logs(start_date=start_date, event_type=event_type, limit=50)
        all_security_events.extend(events)
    
    # Sort by timestamp
    all_security_events.sort(key=lambda x: x['timestamp'], reverse=True)
    
    return all_security_events
