from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import HTTPException

from models.api_key import APIKey, APIKeyScopes
from utils.audit_logger import log_audit_event, AuditEvent

class APIKeyService:
    def __init__(self, db: Session):
        self.db = db
    
    def create_api_key(
        self,
        user_id: str,
        name: str,
        scopes: List[str],
        expires_in_days: Optional[int] = None,
        rate_limit: int = 1000
    ) -> APIKey:
        """Create a new API key for a user"""
        
        # Validate scopes
        if not APIKeyScopes.validate_scopes(scopes):
            raise HTTPException(status_code=400, detail="Invalid scopes provided")
        
        # Generate the raw key
        raw_key = APIKey.generate_key()
        
        # Calculate expiration date
        expires_at = None
        if expires_in_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
        
        # Create API key
        api_key = APIKey(
            name=name,
            key_hash=APIKey.hash_key(raw_key),
            user_id=user_id,
            scopes=",".join(scopes),
            expires_at=expires_at,
            rate_limit=str(rate_limit)
        )
        
        # Store the raw key temporarily for response
        api_key._raw_key = raw_key
        
        self.db.add(api_key)
        self.db.commit()
        self.db.refresh(api_key)
        
        # Log the creation
        log_audit_event(
            event_type="api_key_created",
            user_id=user_id,
            resource_type="api_key",
            resource_id=api_key.id,
            details={
                "name": name,
                "scopes": scopes,
                "expires_at": expires_at.isoformat() if expires_at else None
            }
        )
        
        return api_key
    
    def get_user_api_keys(self, user_id: str) -> List[APIKey]:
        """Get all API keys for a user"""
        return self.db.query(APIKey).filter(
            APIKey.user_id == user_id
        ).order_by(APIKey.created_at.desc()).all()
    
    def get_api_key_by_id(self, key_id: str, user_id: str) -> Optional[APIKey]:
        """Get an API key by ID (only if it belongs to the user)"""
        return self.db.query(APIKey).filter(
            APIKey.id == key_id,
            APIKey.user_id == user_id
        ).first()
    
    def verify_api_key(self, key: str) -> Optional[APIKey]:
        """Verify an API key and return the associated key object"""
        if not key.startswith("tk_"):
            return None
        
        key_hash = APIKey.hash_key(key)
        api_key = self.db.query(APIKey).filter(
            APIKey.key_hash == key_hash,
            APIKey.is_active == True
        ).first()
        
        if not api_key or not api_key.is_valid():
            return None
        
        # Update usage statistics
        api_key.increment_usage()
        self.db.commit()
        
        return api_key
    
    def revoke_api_key(self, key_id: str, user_id: str) -> bool:
        """Revoke (deactivate) an API key"""
        api_key = self.get_api_key_by_id(key_id, user_id)
        
        if not api_key:
            return False
        
        api_key.is_active = False
        self.db.commit()
        
        # Log the revocation
        log_audit_event(
            event_type="api_key_revoked",
            user_id=user_id,
            resource_type="api_key",
            resource_id=key_id,
            details={"name": api_key.name}
        )
        
        return True
    
    def update_api_key(
        self,
        key_id: str,
        user_id: str,
        name: Optional[str] = None,
        scopes: Optional[List[str]] = None,
        rate_limit: Optional[int] = None
    ) -> Optional[APIKey]:
        """Update an API key"""
        api_key = self.get_api_key_by_id(key_id, user_id)
        
        if not api_key:
            return None
        
        changes = {}
        
        if name is not None:
            api_key.name = name
            changes["name"] = name
        
        if scopes is not None:
            if not APIKeyScopes.validate_scopes(scopes):
                raise HTTPException(status_code=400, detail="Invalid scopes provided")
            api_key.scopes = ",".join(scopes)
            changes["scopes"] = scopes
        
        if rate_limit is not None:
            api_key.rate_limit = str(rate_limit)
            changes["rate_limit"] = rate_limit
        
        self.db.commit()
        self.db.refresh(api_key)
        
        # Log the update
        if changes:
            log_audit_event(
                event_type="api_key_updated",
                user_id=user_id,
                resource_type="api_key",
                resource_id=key_id,
                details=changes
            )
        
        return api_key
    
    def delete_api_key(self, key_id: str, user_id: str) -> bool:
        """Permanently delete an API key"""
        api_key = self.get_api_key_by_id(key_id, user_id)
        
        if not api_key:
            return False
        
        key_name = api_key.name
        self.db.delete(api_key)
        self.db.commit()
        
        # Log the deletion
        log_audit_event(
            event_type="api_key_deleted",
            user_id=user_id,
            resource_type="api_key",
            resource_id=key_id,
            details={"name": key_name}
        )
        
        return True
    
    def cleanup_expired_keys(self) -> int:
        """Clean up expired API keys"""
        expired_keys = self.db.query(APIKey).filter(
            APIKey.expires_at < datetime.utcnow(),
            APIKey.is_active == True
        ).all()
        
        count = 0
        for key in expired_keys:
            key.is_active = False
            count += 1
        
        self.db.commit()
        
        if count > 0:
            log_audit_event(
                event_type="api_keys_expired",
                details={"expired_count": count}
            )
        
        return count
    
    def get_api_key_usage_stats(self, user_id: str) -> Dict[str, Any]:
        """Get usage statistics for user's API keys"""
        api_keys = self.get_user_api_keys(user_id)
        
        total_keys = len(api_keys)
        active_keys = len([k for k in api_keys if k.is_active and k.is_valid()])
        total_usage = sum(int(k.usage_count) for k in api_keys if k.usage_count.isdigit())
        
        # Get recent usage (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent_usage = len([
            k for k in api_keys 
            if k.last_used_at and k.last_used_at > thirty_days_ago
        ])
        
        return {
            "total_keys": total_keys,
            "active_keys": active_keys,
            "total_usage": total_usage,
            "recent_usage": recent_usage,
            "keys": [k.to_dict() for k in api_keys]
        }

# API Key authentication dependency
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer(auto_error=False)

async def get_api_key_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[APIKey]:
    """Dependency to get user from API key"""
    if not credentials:
        return None
    
    api_key_service = APIKeyService(db)
    api_key = api_key_service.verify_api_key(credentials.credentials)
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    
    return api_key

def require_api_key_scope(required_scope: str):
    """Dependency factory to require specific API key scope"""
    async def check_scope(api_key: APIKey = Depends(get_api_key_user)):
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API key required"
            )
        
        if not api_key.has_scope(required_scope):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"API key does not have required scope: {required_scope}"
            )
        
        return api_key
    
    return check_scope
