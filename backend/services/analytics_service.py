from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_
from typing import Dict, List, Any
from datetime import datetime, timedelta
from collections import defaultdict

from models.database import Video, Tag, video_tags
from models.schemas import AnalyticsResponse

class AnalyticsService:
    def __init__(self, db: Session):
        self.db = db
    
    def get_analytics(self, user_id: str, start_date: datetime, end_date: datetime) -> AnalyticsResponse:
        """Get comprehensive analytics data"""
        return AnalyticsResponse(
            total_videos=self.get_total_videos(user_id),
            total_tags=self.get_total_tags(user_id),
            total_duration=self.get_total_duration(user_id),
            processed_videos=self.get_processed_videos_count(user_id),
            pending_videos=self.get_pending_videos_count(user_id),
            top_tags=self.get_top_tags_data(user_id, 10),
            language_distribution=self.get_language_distribution(user_id),
            upload_timeline=self.get_upload_timeline(user_id, start_date, end_date),
            duration_distribution=self.get_duration_distribution(user_id)
        )
    
    def get_summary(self) -> Dict[str, Any]:
        """Get basic analytics summary"""
        return {
            "total_videos": self.get_total_videos(),
            "total_tags": self.get_total_tags(),
            "processed_videos": self.get_processed_videos_count(),
            "pending_videos": self.get_pending_videos_count(),
            "total_duration": self.get_total_duration(),
            "average_duration": self.get_average_duration()
        }
    
    def get_total_videos(self, user_id: str) -> int:
        """Get total number of videos for a user"""
        return self.db.query(Video).filter(Video.user_id == user_id).count()
    
    def get_total_tags(self, user_id: str) -> int:
        """Get total number of tags for a user (user-specific + global)"""
        return self.db.query(Tag).filter(
            (Tag.user_id == user_id) | (Tag.user_id.is_(None))
        ).count()
    
    def get_processed_videos_count(self, user_id: str) -> int:
        """Get number of processed videos for a user"""
        return self.db.query(Video).filter(
            Video.user_id == user_id,
            Video.processed == True
        ).count()
    
    def get_pending_videos_count(self, user_id: str) -> int:
        """Get number of pending videos for a user"""
        return self.db.query(Video).filter(
            Video.user_id == user_id,
            Video.processed == False
        ).count()
    
    def get_total_duration(self, user_id: str) -> float:
        """Get total duration of all videos for a user in seconds"""
        result = self.db.query(func.sum(Video.duration)).filter(
            Video.user_id == user_id
        ).scalar()
        return result or 0.0
    
    def get_average_duration(self) -> float:
        """Get average duration of videos in seconds"""
        result = self.db.query(func.avg(Video.duration)).scalar()
        return result or 0.0
    
    def get_top_tags(self, limit: int = 10) -> List[Tag]:
        """Get top tags by usage count"""
        return self.db.query(Tag).filter(
            Tag.usage_count > 0
        ).order_by(desc(Tag.usage_count)).limit(limit).all()
    
    def get_top_tags_data(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top tags data for analytics for a specific user"""
        tags = self.get_top_tags_for_user(user_id, limit)
        return [
            {
                "id": tag.id,
                "name": tag.name,
                "color": tag.color,
                "usage_count": tag.usage_count,
                "description": tag.description
            }
            for tag in tags
        ]

    def get_top_tags_for_user(self, user_id: str, limit: int = 10) -> List[Tag]:
        """Get top tags by usage count for a specific user"""
        # Get tags that are either user-specific or global, ordered by usage
        return self.db.query(Tag).filter(
            (Tag.user_id == user_id) | (Tag.user_id.is_(None))
        ).filter(
            Tag.usage_count > 0
        ).order_by(desc(Tag.usage_count)).limit(limit).all()
    
    def get_language_distribution(self, user_id: str) -> Dict[str, int]:
        """Get distribution of transcript languages for a specific user"""
        results = self.db.query(
            Video.transcript_language,
            func.count(Video.id)
        ).filter(
            Video.user_id == user_id,
            Video.transcript_language.isnot(None)
        ).group_by(Video.transcript_language).all()

        distribution = {}
        for language, count in results:
            distribution[language or "unknown"] = count

        return distribution
    
    def get_upload_timeline(self, user_id: str, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """Get video upload timeline for a specific user"""
        # Group by date
        results = self.db.query(
            func.date(Video.upload_date).label('date'),
            func.count(Video.id).label('count')
        ).filter(
            and_(
                Video.user_id == user_id,
                Video.upload_date >= start_date,
                Video.upload_date <= end_date
            )
        ).group_by(func.date(Video.upload_date)).order_by('date').all()

        timeline = []
        for date, count in results:
            timeline.append({
                "date": str(date) if date else None,
                "count": count
            })

        return timeline
    
    def get_duration_distribution(self) -> Dict[str, int]:
        """Get video duration distribution in buckets"""
        videos = self.db.query(Video.duration).filter(
            Video.duration.isnot(None)
        ).all()
        
        # Define duration buckets (in seconds)
        buckets = {
            "0-30s": 0,
            "30s-1m": 0,
            "1-2m": 0,
            "2-5m": 0,
            "5-10m": 0,
            "10m+": 0
        }
        
        for (duration,) in videos:
            if duration <= 30:
                buckets["0-30s"] += 1
            elif duration <= 60:
                buckets["30s-1m"] += 1
            elif duration <= 120:
                buckets["1-2m"] += 1
            elif duration <= 300:
                buckets["2-5m"] += 1
            elif duration <= 600:
                buckets["5-10m"] += 1
            else:
                buckets["10m+"] += 1
        
        return buckets
    
    def get_processing_status(self) -> Dict[str, int]:
        """Get video processing status breakdown"""
        results = self.db.query(
            Video.processing_status,
            func.count(Video.id)
        ).group_by(Video.processing_status).all()
        
        status_counts = {}
        for status, count in results:
            status_counts[status] = count
        
        return status_counts
    
    def get_monthly_stats(self, months: int = 12) -> List[Dict[str, Any]]:
        """Get monthly upload statistics"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=months * 30)
        
        results = self.db.query(
            func.extract('year', Video.upload_date).label('year'),
            func.extract('month', Video.upload_date).label('month'),
            func.count(Video.id).label('count'),
            func.sum(Video.duration).label('total_duration')
        ).filter(
            Video.upload_date >= start_date
        ).group_by(
            func.extract('year', Video.upload_date),
            func.extract('month', Video.upload_date)
        ).order_by('year', 'month').all()
        
        monthly_stats = []
        for year, month, count, total_duration in results:
            monthly_stats.append({
                "year": int(year),
                "month": int(month),
                "video_count": count,
                "total_duration": total_duration or 0,
                "average_duration": (total_duration / count) if count > 0 and total_duration else 0
            })
        
        return monthly_stats
    
    def get_tag_usage_trends(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get tag usage trends over time"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Get videos uploaded in the time period with their tags
        videos_with_tags = self.db.query(Video).filter(
            Video.upload_date >= start_date
        ).all()
        
        # Count tag usage by day
        daily_tag_usage = defaultdict(lambda: defaultdict(int))
        
        for video in videos_with_tags:
            date_key = video.upload_date.date().isoformat()
            for tag in video.tags:
                daily_tag_usage[date_key][tag.name] += 1
        
        # Convert to list format
        trends = []
        for date, tag_counts in daily_tag_usage.items():
            for tag_name, count in tag_counts.items():
                trends.append({
                    "date": date,
                    "tag_name": tag_name,
                    "usage_count": count
                })
        
        return sorted(trends, key=lambda x: x["date"])
