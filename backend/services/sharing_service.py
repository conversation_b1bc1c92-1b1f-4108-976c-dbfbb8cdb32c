from sqlalchemy.orm import Session, joinedload
from typing import List, Optional, Dict, Any
from datetime import datetime

from models.database import SharedVideo, Video, User
from models.schemas import VideoShareRequest, VideoShareResponse

class SharingService:
    def __init__(self, db: Session):
        self.db = db
    
    def share_video(self, video_id: int, owner_id: str, share_request: VideoShareRequest) -> SharedVideo:
        """Share a video with another user"""
        
        # Verify the video belongs to the owner
        video = self.db.query(Video).filter(
            Video.id == video_id,
            Video.user_id == owner_id
        ).first()
        
        if not video:
            raise ValueError("Video not found or you don't have permission to share it")
        
        # Find the user to share with
        shared_with_user = self.db.query(User).filter(
            User.email == share_request.shared_with_email
        ).first()
        
        if not shared_with_user:
            raise ValueError(f"User with email {share_request.shared_with_email} not found")
        
        if shared_with_user.id == owner_id:
            raise ValueError("You cannot share a video with yourself")
        
        # Check if already shared
        existing_share = self.db.query(SharedVideo).filter(
            SharedVideo.video_id == video_id,
            SharedVideo.shared_with_user_id == shared_with_user.id
        ).first()
        
        if existing_share:
            # Update existing share
            existing_share.permissions = share_request.permissions
            existing_share.expires_at = share_request.expires_at
            existing_share.shared_at = datetime.utcnow()
            self.db.commit()
            self.db.refresh(existing_share)
            return existing_share
        
        # Create new share
        shared_video = SharedVideo(
            video_id=video_id,
            owner_id=owner_id,
            shared_with_user_id=shared_with_user.id,
            permissions=share_request.permissions,
            expires_at=share_request.expires_at
        )
        
        self.db.add(shared_video)
        self.db.commit()
        self.db.refresh(shared_video)
        return shared_video
    
    def unshare_video(self, video_id: int, owner_id: str, shared_with_user_id: str) -> bool:
        """Remove video sharing with a specific user"""
        
        # Verify the video belongs to the owner
        video = self.db.query(Video).filter(
            Video.id == video_id,
            Video.user_id == owner_id
        ).first()
        
        if not video:
            return False
        
        # Find and delete the share
        shared_video = self.db.query(SharedVideo).filter(
            SharedVideo.video_id == video_id,
            SharedVideo.owner_id == owner_id,
            SharedVideo.shared_with_user_id == shared_with_user_id
        ).first()
        
        if shared_video:
            self.db.delete(shared_video)
            self.db.commit()
            return True
        
        return False

    def unshare_video_by_email(self, video_id: int, owner_id: str, shared_with_email: str) -> bool:
        """Remove video sharing with a specific user by email"""

        # Verify the video belongs to the owner
        video = self.db.query(Video).filter(
            Video.id == video_id,
            Video.user_id == owner_id
        ).first()

        if not video:
            return False

        # Find the user by email
        shared_with_user = self.db.query(User).filter(
            User.email == shared_with_email
        ).first()

        if not shared_with_user:
            return False

        # Find and delete the share
        shared_video = self.db.query(SharedVideo).filter(
            SharedVideo.video_id == video_id,
            SharedVideo.owner_id == owner_id,
            SharedVideo.shared_with_user_id == shared_with_user.id
        ).first()

        if shared_video:
            self.db.delete(shared_video)
            self.db.commit()
            return True

        return False

    def remove_shared_video_for_user(self, video_id: int, user_id: str) -> bool:
        """Remove a shared video from a user's 'Shared with Me' list"""

        # Find the share where this user is the recipient
        shared_video = self.db.query(SharedVideo).filter(
            SharedVideo.video_id == video_id,
            SharedVideo.shared_with_user_id == user_id
        ).first()

        if shared_video:
            self.db.delete(shared_video)
            self.db.commit()
            return True

        return False

    def get_videos_shared_by_user(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all videos shared by a user"""
        
        shares = self.db.query(SharedVideo).options(
            joinedload(SharedVideo.video),
            joinedload(SharedVideo.shared_with)
        ).filter(
            SharedVideo.owner_id == user_id
        ).all()
        
        # Group by video
        videos_shared = {}
        for share in shares:
            video_id = share.video_id
            if video_id not in videos_shared:
                # Serialize video data properly
                video_data = {
                    'id': share.video.id,
                    'title': share.video.title,
                    'original_filename': share.video.original_filename,
                    'filename': share.video.filename,
                    'file_path': share.video.file_path,
                    'file_size': share.video.file_size,
                    'duration': share.video.duration,
                    'width': share.video.width,
                    'height': share.video.height,
                    'fps': share.video.fps,
                    'thumbnail_path': share.video.thumbnail_path,
                    'transcript': share.video.transcript,
                    'transcript_language': share.video.transcript_language,
                    'upload_date': share.video.upload_date,
                    'processed': share.video.processed,
                    'processing_status': share.video.processing_status,
                    'processing_progress': share.video.processing_progress,
                    'source_url': share.video.source_url,
                    'download_status': share.video.download_status,
                    'download_error': share.video.download_error,
                    'download_progress': share.video.download_progress
                }

                videos_shared[video_id] = {
                    'video': video_data,
                    'shared_with': []
                }

            videos_shared[video_id]['shared_with'].append({
                'user_id': share.shared_with_user_id,
                'email': share.shared_with.email,
                'full_name': share.shared_with.full_name,
                'permissions': share.permissions,
                'shared_at': share.shared_at,
                'expires_at': share.expires_at
            })

        return list(videos_shared.values())
    
    def get_videos_shared_with_user(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all videos shared with a user"""
        
        shares = self.db.query(SharedVideo).options(
            joinedload(SharedVideo.video),
            joinedload(SharedVideo.owner)
        ).filter(
            SharedVideo.shared_with_user_id == user_id
        ).all()
        
        # Filter out expired shares
        active_shares = []
        for share in shares:
            if share.expires_at is None or share.expires_at > datetime.utcnow():
                # Serialize video data properly
                video_data = {
                    'id': share.video.id,
                    'title': share.video.title,
                    'original_filename': share.video.original_filename,
                    'filename': share.video.filename,
                    'file_path': share.video.file_path,
                    'file_size': share.video.file_size,
                    'duration': share.video.duration,
                    'width': share.video.width,
                    'height': share.video.height,
                    'fps': share.video.fps,
                    'thumbnail_path': share.video.thumbnail_path,
                    'transcript': share.video.transcript,
                    'transcript_language': share.video.transcript_language,
                    'upload_date': share.video.upload_date,
                    'processed': share.video.processed,
                    'processing_status': share.video.processing_status,
                    'processing_progress': share.video.processing_progress,
                    'source_url': share.video.source_url,
                    'download_status': share.video.download_status,
                    'download_error': share.video.download_error,
                    'download_progress': share.video.download_progress
                }

                active_shares.append({
                    'video': video_data,
                    'owner': {
                        'user_id': share.owner_id,
                        'email': share.owner.email,
                        'full_name': share.owner.full_name
                    },
                    'permissions': share.permissions,
                    'shared_at': share.shared_at,
                    'expires_at': share.expires_at
                })

        return active_shares
    
    def get_video_shares(self, video_id: int, owner_id: str) -> List[VideoShareResponse]:
        """Get all shares for a specific video"""
        
        # Verify the video belongs to the owner
        video = self.db.query(Video).filter(
            Video.id == video_id,
            Video.user_id == owner_id
        ).first()
        
        if not video:
            return []
        
        shares = self.db.query(SharedVideo).options(
            joinedload(SharedVideo.shared_with),
            joinedload(SharedVideo.owner)
        ).filter(
            SharedVideo.video_id == video_id,
            SharedVideo.owner_id == owner_id
        ).all()
        
        return [
            VideoShareResponse(
                id=share.id,
                video_id=share.video_id,
                owner_email=share.owner.email,
                shared_with_email=share.shared_with.email,
                permissions=share.permissions,
                shared_at=share.shared_at,
                expires_at=share.expires_at
            )
            for share in shares
        ]
    
    def can_user_access_video(self, video_id: int, user_id: str) -> Dict[str, Any]:
        """Check if a user can access a video and with what permissions"""
        
        # Check if user owns the video
        video = self.db.query(Video).filter(
            Video.id == video_id,
            Video.user_id == user_id
        ).first()
        
        if video:
            return {
                'can_access': True,
                'permissions': 'owner',
                'is_owner': True
            }
        
        # Check if video is shared with the user
        share = self.db.query(SharedVideo).filter(
            SharedVideo.video_id == video_id,
            SharedVideo.shared_with_user_id == user_id
        ).first()
        
        if share:
            # Check if share is not expired
            if share.expires_at is None or share.expires_at > datetime.utcnow():
                return {
                    'can_access': True,
                    'permissions': share.permissions,
                    'is_owner': False,
                    'shared_by': share.owner_id
                }
        
        return {
            'can_access': False,
            'permissions': None,
            'is_owner': False
        }
    
    def search_users_for_sharing(self, query: str, current_user_id: str, limit: int = 10) -> List[Dict[str, str]]:
        """Search for users to share videos with"""
        
        search_term = f"%{query}%"
        users = self.db.query(User).filter(
            User.id != current_user_id,  # Exclude current user
            User.is_active == True,
            (User.email.ilike(search_term) | User.full_name.ilike(search_term))
        ).limit(limit).all()
        
        return [
            {
                'id': user.id,
                'email': user.email,
                'full_name': user.full_name or user.email
            }
            for user in users
        ]
