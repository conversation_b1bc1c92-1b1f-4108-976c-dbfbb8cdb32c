from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime, timedelta

from models.database import get_db, User
from models.schemas import AnalyticsResponse
from services.analytics_service import AnalyticsService
from utils.auth import get_current_user

router = APIRouter()

@router.get("/", response_model=AnalyticsResponse)
async def get_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive analytics data"""
    analytics_service = AnalyticsService(db)
    
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    return analytics_service.get_analytics(current_user.id, start_date, end_date)

@router.get("/summary")
async def get_analytics_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get basic analytics summary"""
    analytics_service = AnalyticsService(db)
    return analytics_service.get_summary(current_user.id)

@router.get("/tags/top")
async def get_top_tags(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get top tags by usage"""
    analytics_service = AnalyticsService(db)
    return analytics_service.get_top_tags_for_user(current_user.id, limit)

@router.get("/videos/timeline")
async def get_upload_timeline(
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get video upload timeline"""
    analytics_service = AnalyticsService(db)

    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)

    return analytics_service.get_upload_timeline(current_user.id, start_date, end_date)

@router.get("/videos/duration-distribution")
async def get_duration_distribution(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get video duration distribution"""
    analytics_service = AnalyticsService(db)
    return analytics_service.get_duration_distribution(current_user.id)

@router.get("/languages")
async def get_language_distribution(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get language distribution from transcripts"""
    analytics_service = AnalyticsService(db)
    return analytics_service.get_language_distribution(current_user.id)

@router.get("/processing-status")
async def get_processing_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get video processing status breakdown"""
    analytics_service = AnalyticsService(db)
    return analytics_service.get_processing_status(current_user.id)
