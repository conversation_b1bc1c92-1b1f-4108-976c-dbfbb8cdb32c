from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel, Field

from models.database import get_db, User
from models.api_key import APIKeyScopes
from services.api_key_service import APIKeyService
from utils.auth import get_current_user

router = APIRouter()

# Pydantic models for API key management
class APIKeyCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100, description="Human-readable name for the API key")
    scopes: List[str] = Field(..., description="List of scopes for the API key")
    expires_in_days: Optional[int] = Field(None, ge=1, le=365, description="Number of days until expiration")
    rate_limit: int = Field(1000, ge=1, le=10000, description="Requests per hour limit")

class APIKeyUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    scopes: Optional[List[str]] = None
    rate_limit: Optional[int] = Field(None, ge=1, le=10000)

class APIKeyResponse(BaseModel):
    id: str
    name: str
    scopes: List[str]
    is_active: bool
    created_at: str
    last_used_at: Optional[str]
    expires_at: Optional[str]
    usage_count: str
    rate_limit: str

class APIKeyCreateResponse(APIKeyResponse):
    key: str  # Only returned when creating a new key

class ScopeInfo(BaseModel):
    scope: str
    description: str

@router.get("/scopes", response_model=List[ScopeInfo])
async def get_available_scopes():
    """Get list of available API key scopes"""
    return [
        ScopeInfo(scope=scope, description=APIKeyScopes.get_scope_description(scope))
        for scope in APIKeyScopes.ALL_SCOPES
    ]

@router.post("/", response_model=APIKeyCreateResponse)
async def create_api_key(
    key_data: APIKeyCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new API key"""
    api_key_service = APIKeyService(db)
    
    # Validate scopes
    if not APIKeyScopes.validate_scopes(key_data.scopes):
        raise HTTPException(
            status_code=400,
            detail="Invalid scopes. Use /api-keys/scopes to see available scopes."
        )
    
    # Check if user already has too many API keys
    existing_keys = api_key_service.get_user_api_keys(current_user.id)
    active_keys = [k for k in existing_keys if k.is_active and k.is_valid()]
    
    if len(active_keys) >= 10:  # Limit to 10 active keys per user
        raise HTTPException(
            status_code=400,
            detail="Maximum number of API keys reached. Please revoke unused keys first."
        )
    
    # Create the API key
    api_key = api_key_service.create_api_key(
        user_id=current_user.id,
        name=key_data.name,
        scopes=key_data.scopes,
        expires_in_days=key_data.expires_in_days,
        rate_limit=key_data.rate_limit
    )
    
    # Return the key data including the raw key (only time it's shown)
    response_data = api_key.to_dict(include_key=True)
    return APIKeyCreateResponse(**response_data)

@router.get("/", response_model=List[APIKeyResponse])
async def get_api_keys(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all API keys for the current user"""
    api_key_service = APIKeyService(db)
    api_keys = api_key_service.get_user_api_keys(current_user.id)
    
    return [APIKeyResponse(**key.to_dict()) for key in api_keys]

@router.get("/stats")
async def get_api_key_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get API key usage statistics"""
    api_key_service = APIKeyService(db)
    return api_key_service.get_api_key_usage_stats(current_user.id)

@router.get("/{key_id}", response_model=APIKeyResponse)
async def get_api_key(
    key_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific API key"""
    api_key_service = APIKeyService(db)
    api_key = api_key_service.get_api_key_by_id(key_id, current_user.id)
    
    if not api_key:
        raise HTTPException(status_code=404, detail="API key not found")
    
    return APIKeyResponse(**api_key.to_dict())

@router.patch("/{key_id}", response_model=APIKeyResponse)
async def update_api_key(
    key_id: str,
    key_data: APIKeyUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an API key"""
    api_key_service = APIKeyService(db)
    
    # Validate scopes if provided
    if key_data.scopes and not APIKeyScopes.validate_scopes(key_data.scopes):
        raise HTTPException(
            status_code=400,
            detail="Invalid scopes. Use /api-keys/scopes to see available scopes."
        )
    
    api_key = api_key_service.update_api_key(
        key_id=key_id,
        user_id=current_user.id,
        name=key_data.name,
        scopes=key_data.scopes,
        rate_limit=key_data.rate_limit
    )
    
    if not api_key:
        raise HTTPException(status_code=404, detail="API key not found")
    
    return APIKeyResponse(**api_key.to_dict())

@router.post("/{key_id}/revoke")
async def revoke_api_key(
    key_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Revoke (deactivate) an API key"""
    api_key_service = APIKeyService(db)
    
    success = api_key_service.revoke_api_key(key_id, current_user.id)
    
    if not success:
        raise HTTPException(status_code=404, detail="API key not found")
    
    return {"message": "API key revoked successfully"}

@router.delete("/{key_id}")
async def delete_api_key(
    key_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Permanently delete an API key"""
    api_key_service = APIKeyService(db)
    
    success = api_key_service.delete_api_key(key_id, current_user.id)
    
    if not success:
        raise HTTPException(status_code=404, detail="API key not found")
    
    return {"message": "API key deleted successfully"}

# Admin endpoints (if needed)
@router.post("/cleanup-expired")
async def cleanup_expired_keys(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Clean up expired API keys (admin only)"""
    # This could be restricted to admin users
    api_key_service = APIKeyService(db)
    count = api_key_service.cleanup_expired_keys()
    
    return {"message": f"Cleaned up {count} expired API keys"}
