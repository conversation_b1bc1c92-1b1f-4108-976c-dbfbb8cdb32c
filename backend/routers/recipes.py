from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, func, text
from typing import List, Optional
from models.database import get_db, Recipe, Video, User, SharedVideo
from models.schemas import RecipeResponse, RecipeUpdate, RecipeCreate, RecipeWithVideoResponse
from services.recipe_service import RecipeService
from services.video_service import VideoService
from utils.auth import get_current_user

router = APIRouter(tags=["recipes"])


@router.get("/", response_model=List[RecipeResponse])
async def get_recipes(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    cuisine: Optional[str] = Query(None, description="Filter by cuisine type"),
    difficulty: Optional[str] = Query(None, description="Filter by difficulty"),
    search: Optional[str] = Query(None, description="Search in title, description, or ingredients"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get recipes with optional filtering"""
    recipe_service = RecipeService(db)
    
    if search:
        recipes = recipe_service.search_recipes(search, limit)
    elif cuisine:
        recipes = recipe_service.get_recipes_by_cuisine(cuisine, limit)
    elif difficulty:
        recipes = recipe_service.get_recipes_by_difficulty(difficulty, limit)
    else:
        recipes = recipe_service.get_all_recipes(current_user.id, skip, limit)
    
    return [recipe_service.convert_to_response(recipe) for recipe in recipes]


@router.get("/with-videos", response_model=List[RecipeWithVideoResponse])
async def get_recipes_with_videos(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    cuisine: Optional[str] = Query(None, description="Filter by cuisine type"),
    difficulty: Optional[str] = Query(None, description="Filter by difficulty"),
    search: Optional[str] = Query(None, description="Search in title, description, or ingredients"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get recipes with video information for the recipes page"""
    try:
        # Filter by user - show recipes for videos owned by the current user OR shared with them
        query = db.query(Recipe).join(Video).filter(
            or_(
                # Videos owned by the user
                Video.user_id == current_user.id,
                # Videos shared with the user (non-expired shares)
                Video.id.in_(
                    db.query(SharedVideo.video_id).filter(
                        SharedVideo.shared_with_user_id == current_user.id,
                        or_(
                            SharedVideo.expires_at.is_(None),
                            SharedVideo.expires_at > func.now()
                        )
                    )
                )
            )
        ).options(joinedload(Recipe.video))

        # Apply filters
        if search:
            search_term = f"%{search}%"
            # Use func.json_extract for SQLite or convert to text for other databases
            query = query.filter(
                Recipe.title.ilike(search_term) |
                Recipe.description.ilike(search_term) |
                func.json_extract(Recipe.ingredients, '$').like(search_term)
            )

        if cuisine:
            query = query.filter(Recipe.cuisine_type.ilike(f"%{cuisine}%"))

        if difficulty:
            query = query.filter(Recipe.difficulty.ilike(f"%{difficulty}%"))

        # Apply pagination
        recipes = query.offset(skip).limit(limit).all()

        # Convert to response format
        result = []
        recipe_service = RecipeService(db)

        for recipe in recipes:
            try:
                recipe_data = recipe_service.convert_to_response(recipe)

                # Add video information
                recipe_with_video = RecipeWithVideoResponse(
                    **recipe_data.dict(),
                    video_title=recipe.video.title if recipe.video else None,
                    video_thumbnail_path=recipe.video.thumbnail_path if recipe.video else None,
                    video_duration=recipe.video.duration if recipe.video else None
                )
                result.append(recipe_with_video)
            except Exception as e:
                print(f"Error processing recipe {recipe.id}: {e}")
                # Skip recipes that can't be processed
                continue

        return result

    except Exception as e:
        print(f"Error in get_recipes_with_videos: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch recipes: {str(e)}")


@router.get("/video/{video_id}", response_model=RecipeResponse)
async def get_recipe_by_video(
    video_id: int,
    db: Session = Depends(get_db)
):
    """Get recipe for a specific video"""
    recipe_service = RecipeService(db)
    video_service = VideoService(db)
    
    # Check if video exists
    video = video_service.get_video_by_id(video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    recipe = recipe_service.get_recipe_by_video_id(video_id)
    if not recipe:
        raise HTTPException(status_code=404, detail="No recipe found for this video")
    
    return recipe_service.convert_to_response(recipe)


@router.get("/{recipe_id}", response_model=RecipeResponse)
async def get_recipe(
    recipe_id: int,
    db: Session = Depends(get_db)
):
    """Get recipe by ID"""
    recipe_service = RecipeService(db)
    
    recipe = recipe_service.get_recipe_by_id(recipe_id)
    if not recipe:
        raise HTTPException(status_code=404, detail="Recipe not found")
    
    return recipe_service.convert_to_response(recipe)


@router.put("/{recipe_id}", response_model=RecipeResponse)
async def update_recipe(
    recipe_id: int,
    recipe_data: RecipeUpdate,
    db: Session = Depends(get_db)
):
    """Update a recipe"""
    recipe_service = RecipeService(db)
    
    recipe = recipe_service.update_recipe(recipe_id, recipe_data)
    if not recipe:
        raise HTTPException(status_code=404, detail="Recipe not found")
    
    return recipe_service.convert_to_response(recipe)


@router.delete("/{recipe_id}")
async def delete_recipe(
    recipe_id: int,
    db: Session = Depends(get_db)
):
    """Delete a recipe"""
    recipe_service = RecipeService(db)
    
    success = recipe_service.delete_recipe(recipe_id)
    if not success:
        raise HTTPException(status_code=404, detail="Recipe not found")
    
    return {"message": "Recipe deleted successfully"}


@router.post("/", response_model=RecipeResponse)
async def create_recipe(
    recipe_data: RecipeCreate,
    db: Session = Depends(get_db)
):
    """Create a new recipe manually"""
    recipe_service = RecipeService(db)
    video_service = VideoService(db)
    
    # Check if video exists
    video = video_service.get_video_by_id(recipe_data.video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Check if recipe already exists for this video
    existing_recipe = recipe_service.get_recipe_by_video_id(recipe_data.video_id)
    if existing_recipe:
        raise HTTPException(status_code=400, detail="Recipe already exists for this video")
    
    try:
        recipe = recipe_service.create_recipe(recipe_data)
        return recipe_service.convert_to_response(recipe)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create recipe: {str(e)}")


@router.get("/stats/overview")
async def get_recipe_stats(db: Session = Depends(get_db)):
    """Get recipe statistics"""
    try:
        recipe_service = RecipeService(db)
        return recipe_service.get_recipe_stats()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get recipe stats: {str(e)}")


@router.get("/cuisines/list")
async def get_cuisine_types(db: Session = Depends(get_db)):
    """Get list of available cuisine types"""
    from models.database import Recipe

    # Get distinct cuisine types
    cuisines = db.query(Recipe.cuisine_type).filter(
        Recipe.cuisine_type.isnot(None)
    ).distinct().all()

    return [cuisine[0] for cuisine in cuisines if cuisine[0]]


@router.get("/difficulties/list")
async def get_difficulty_levels(db: Session = Depends(get_db)):
    """Get list of available difficulty levels"""
    return ["Easy", "Medium", "Hard"]


@router.post("/video/{video_id}/extract")
async def extract_recipe_for_video(
    video_id: int,
    force: bool = Query(False, description="Force re-extraction even if recipe exists"),
    db: Session = Depends(get_db)
):
    """Manually trigger recipe extraction for a video"""
    from utils.recipe_extractor import RecipeExtractor
    
    recipe_service = RecipeService(db)
    video_service = VideoService(db)
    
    # Check if video exists
    video = video_service.get_video_by_id(video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    if not video.transcript:
        raise HTTPException(status_code=400, detail="Video has no transcript")
    
    # Check if recipe already exists
    existing_recipe = recipe_service.get_recipe_by_video_id(video_id)
    if existing_recipe and not force:
        raise HTTPException(status_code=400, detail="Recipe already exists for this video. Use force=true to re-extract.")

    # If force=true and recipe exists, delete the existing recipe first
    if existing_recipe and force:
        recipe_service.delete_recipe(existing_recipe.id)
    
    try:
        recipe_extractor = RecipeExtractor()
        recipe_create = await recipe_extractor.extract_recipe(
            video.transcript, 
            video.title or video.original_filename, 
            video_id
        )
        
        if recipe_create:
            recipe = recipe_service.create_recipe(recipe_create)
            return {
                "message": "Recipe extracted successfully",
                "recipe": recipe_service.convert_to_response(recipe)
            }
        else:
            return {
                "message": "No recipe detected in this video",
                "recipe": None
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Recipe extraction failed: {str(e)}")


@router.get("/search/ingredients")
async def search_by_ingredients(
    ingredients: str = Query(..., description="Comma-separated list of ingredients"),
    db: Session = Depends(get_db)
):
    """Search recipes by ingredients"""
    try:
        recipe_service = RecipeService(db)

        # Split ingredients and search
        ingredient_list = [ing.strip() for ing in ingredients.split(",")]
        recipes = []

        for ingredient in ingredient_list:
            found_recipes = recipe_service.search_recipes(ingredient, 50)
            recipes.extend(found_recipes)

        # Remove duplicates and convert to response
        unique_recipes = list({recipe.id: recipe for recipe in recipes}.values())

        return [recipe_service.convert_to_response(recipe) for recipe in unique_recipes[:20]]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to search recipes: {str(e)}")
