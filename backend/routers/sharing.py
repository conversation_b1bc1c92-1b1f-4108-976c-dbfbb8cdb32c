from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any

from models.database import get_db, User
from models.schemas import VideoShareRequest, VideoShareResponse
from services.sharing_service import SharingService
from utils.auth import get_current_user

router = APIRouter()

@router.post("/videos/{video_id}/share", response_model=VideoShareResponse)
async def share_video(
    video_id: int,
    share_request: VideoShareRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share a video with another user"""
    sharing_service = SharingService(db)
    
    try:
        shared_video = sharing_service.share_video(video_id, current_user.id, share_request)
        
        return VideoShareResponse(
            id=shared_video.id,
            video_id=shared_video.video_id,
            owner_email=current_user.email,
            shared_with_email=share_request.shared_with_email,
            permissions=shared_video.permissions,
            shared_at=shared_video.shared_at,
            expires_at=shared_video.expires_at
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/videos/{video_id}/share/{shared_with_email}")
async def unshare_video(
    video_id: int,
    shared_with_email: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Remove video sharing with a specific user by email"""
    sharing_service = SharingService(db)

    success = sharing_service.unshare_video_by_email(video_id, current_user.id, shared_with_email)

    if not success:
        raise HTTPException(status_code=404, detail="Share not found or you don't have permission")

    return {"message": "Video unshared successfully"}

@router.get("/videos/{video_id}/shares", response_model=List[VideoShareResponse])
async def get_video_shares(
    video_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all shares for a specific video"""
    sharing_service = SharingService(db)
    return sharing_service.get_video_shares(video_id, current_user.id)

@router.get("/shared-by-me")
async def get_videos_shared_by_me(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """Get all videos shared by the current user"""
    sharing_service = SharingService(db)
    return sharing_service.get_videos_shared_by_user(current_user.id)

@router.get("/shared-with-me")
async def get_videos_shared_with_me(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> List[Dict[str, Any]]:
    """Get all videos shared with the current user"""
    sharing_service = SharingService(db)
    return sharing_service.get_videos_shared_with_user(current_user.id)

@router.get("/videos/{video_id}/access")
async def check_video_access(
    video_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """Check if current user can access a video and with what permissions"""
    sharing_service = SharingService(db)
    return sharing_service.can_user_access_video(video_id, current_user.id)

@router.delete("/shared-with-me/{video_id}")
async def remove_shared_video(
    video_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Remove a video from the current user's 'Shared with Me' list"""
    sharing_service = SharingService(db)

    success = sharing_service.remove_shared_video_for_user(video_id, current_user.id)

    if not success:
        raise HTTPException(status_code=404, detail="Shared video not found or you don't have permission")

    return {"message": "Video removed from shared list successfully"}

@router.get("/users/search")
async def search_users_for_sharing(
    q: str = Query(..., min_length=2, description="Search query for user email or name"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> List[Dict[str, str]]:
    """Search for users to share videos with"""
    sharing_service = SharingService(db)
    return sharing_service.search_users_for_sharing(q, current_user.id, limit)
