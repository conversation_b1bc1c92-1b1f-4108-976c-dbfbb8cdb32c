from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Float, Boolean, ForeignKey, Table, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import os
import uuid

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")

# Create engine with appropriate configuration
if "postgresql" in DATABASE_URL or "supabase" in DATABASE_URL:
    # PostgreSQL/Supabase configuration
    engine = create_engine(
        DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=300,
        echo=False
    )
else:
    # SQLite configuration
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        echo=False
    )

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# User model for authentication
class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    password_hash = Column(String, nullable=False)
    full_name = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    email_verified = Column(Boolean, default=False)
    reset_token = Column(String, nullable=True)
    reset_token_expires = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)

    # Relationships
    videos = relationship("Video", back_populates="user")
    tags = relationship("Tag", back_populates="user")

# Association table for many-to-many relationship between videos and tags
video_tags = Table(
    'video_tags',
    Base.metadata,
    Column('video_id', Integer, ForeignKey('videos.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True)
)

class Video(Base):
    __tablename__ = "videos"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False, index=True)
    filename = Column(String, unique=True, index=True, nullable=False)
    original_filename = Column(String, nullable=False)
    title = Column(String, nullable=True)
    file_path = Column(String, nullable=False)
    file_size = Column(Integer, nullable=False)
    duration = Column(Float, nullable=True)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    fps = Column(Float, nullable=True)
    thumbnail_path = Column(String, nullable=True)
    transcript = Column(Text, nullable=True)
    transcript_language = Column(String, nullable=True)
    upload_date = Column(DateTime(timezone=True), server_default=func.now())
    processed = Column(Boolean, default=False)
    processing_status = Column(String, default="pending")  # pending, processing, completed, failed
    processing_progress = Column(Integer, default=0)  # 0-100 percentage

    # Download-related fields
    source_url = Column(String, nullable=True)  # URL if downloaded from web
    download_status = Column(String, nullable=True)  # downloading, completed, failed
    download_error = Column(Text, nullable=True)  # Error message if download failed
    download_progress = Column(Integer, default=0)  # 0-100 percentage for downloads

    # Relationships
    user = relationship("User", back_populates="videos")
    tags = relationship("Tag", secondary=video_tags, back_populates="videos")
    recipe = relationship("Recipe", back_populates="video", uselist=False)

class Tag(Base):
    __tablename__ = "tags"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=True, index=True)  # NULL for global tags
    name = Column(String, index=True, nullable=False)  # Removed unique constraint for user-specific tags
    color = Column(String, nullable=False)  # Hex color code
    description = Column(Text, nullable=True)
    created_date = Column(DateTime(timezone=True), server_default=func.now())
    usage_count = Column(Integer, default=0)

    # Relationships
    user = relationship("User", back_populates="tags")
    videos = relationship("Video", secondary=video_tags, back_populates="tags")

class ProcessingJob(Base):
    __tablename__ = "processing_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    job_type = Column(String, nullable=False)  # transcription, tagging, thumbnail
    status = Column(String, default="pending")  # pending, running, completed, failed
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Relationship
    video = relationship("Video")

class Recipe(Base):
    __tablename__ = "recipes"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False, unique=True)
    title = Column(String, nullable=True)
    description = Column(Text, nullable=True)

    # Structured recipe data
    ingredients = Column(JSON, nullable=True)  # List of ingredient objects
    instructions = Column(JSON, nullable=True)  # List of instruction steps

    # Additional recipe metadata
    prep_time = Column(String, nullable=True)  # e.g., "15 minutes"
    cook_time = Column(String, nullable=True)  # e.g., "30 minutes"
    total_time = Column(String, nullable=True)  # e.g., "45 minutes"
    servings = Column(String, nullable=True)   # e.g., "4 servings"
    difficulty = Column(String, nullable=True)  # e.g., "Easy", "Medium", "Hard"
    cuisine_type = Column(String, nullable=True)  # e.g., "Italian", "Mexican"

    # Extraction metadata
    extracted_at = Column(DateTime(timezone=True), server_default=func.now())
    extraction_confidence = Column(Float, nullable=True)  # 0.0 to 1.0

    # Relationship
    video = relationship("Video", back_populates="recipe")

class SharedVideo(Base):
    __tablename__ = "shared_videos"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    owner_id = Column(String, ForeignKey("users.id"), nullable=False)
    shared_with_user_id = Column(String, ForeignKey("users.id"), nullable=False)
    permissions = Column(String, default="view")  # view, download, comment
    shared_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)  # Optional expiration

    # Relationships
    video = relationship("Video")
    owner = relationship("User", foreign_keys=[owner_id])
    shared_with = relationship("User", foreign_keys=[shared_with_user_id])

# Create all tables
def create_tables():
    Base.metadata.create_all(bind=engine)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
