"""
Centralized configuration management for tagTok backend
"""
import os
from typing import List


class Config:
    """Centralized configuration class for environment variables"""

    # Database Configuration
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///db/tagTok.db")

    # Supabase Configuration
    SUPABASE_URL = os.getenv("SUPABASE_URL", "")
    SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY", "")
    SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY", "")

    # JWT Configuration
    JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
    JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "15"))
    JWT_REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))

    # Directory Paths
    VIDEOS_DIR = os.getenv("VIDEOS_DIR", "/app/videos")
    TRANSCRIPTS_DIR = os.getenv("TRANSCRIPTS_DIR", "/app/transcripts")

    # Port Configuration
    BACKEND_INTERNAL_PORT = int(os.getenv("BACKEND_INTERNAL_PORT", "8000"))
    OLLAMA_INTERNAL_PORT = int(os.getenv("OLLAMA_INTERNAL_PORT", "11434"))

    # Service URLs
    BACKEND_URL = os.getenv("BACKEND_URL", "http://backend:8000")
    OLLAMA_URL = os.getenv("OLLAMA_URL", "http://ollama:11434")
    FRONTEND_URL = os.getenv("FRONTEND_URL", "http://frontend:80")

    # AI Model Configuration
    WHISPER_MODEL_SIZE = os.getenv("WHISPER_MODEL_SIZE", "base")
    
    # Development Configuration
    PYTHONPATH = os.getenv("PYTHONPATH", "/app")
    
    @classmethod
    def get_ollama_endpoints(cls) -> List[str]:
        """Get Ollama endpoints with fallbacks for different environments"""
        base_url = cls.OLLAMA_URL
        
        # Extract host and port from OLLAMA_URL
        if "://" in base_url:
            protocol, rest = base_url.split("://", 1)
            if ":" in rest:
                host, port = rest.split(":", 1)
            else:
                host = rest
                port = str(cls.OLLAMA_INTERNAL_PORT)
        else:
            host = "ollama"
            port = str(cls.OLLAMA_INTERNAL_PORT)
        
        # Return endpoints with fallbacks
        endpoints = [
            f"http://{host}:{port}/api/generate",  # Primary endpoint
        ]
        
        # Add localhost fallback for development
        if host != "localhost":
            endpoints.append(f"http://localhost:{port}/api/generate")
        
        return endpoints
    
    @classmethod
    def get_backend_health_url(cls) -> str:
        """Get backend health check URL"""
        return f"http://localhost:{cls.BACKEND_INTERNAL_PORT}/health"
    
    @classmethod
    def print_config(cls):
        """Print current configuration (for debugging)"""
        print("=== tagTok Configuration ===")
        print(f"Database URL: {cls.DATABASE_URL}")
        print(f"Videos Directory: {cls.VIDEOS_DIR}")
        print(f"Transcripts Directory: {cls.TRANSCRIPTS_DIR}")
        print(f"Backend Internal Port: {cls.BACKEND_INTERNAL_PORT}")
        print(f"Backend URL: {cls.BACKEND_URL}")
        print(f"Ollama URL: {cls.OLLAMA_URL}")
        print(f"Ollama Endpoints: {cls.get_ollama_endpoints()}")
        print(f"Frontend URL: {cls.FRONTEND_URL}")
        print(f"Whisper Model Size: {cls.WHISPER_MODEL_SIZE}")
        print("=" * 30)


# Create a global config instance
config = Config()
