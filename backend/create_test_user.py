#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a test user for immediate testing of the multi-user system.
This creates a user with known credentials that can be used for testing.
"""

import sys
import os
from sqlalchemy.orm import Session

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import SessionLocal, create_tables, User
from utils.auth import get_password_hash

def create_test_user():
    """Create a test user with known credentials"""
    
    # Ensure tables exist
    create_tables()
    
    # Create database session
    db: Session = SessionLocal()
    
    try:
        # Test user credentials
        test_email = "<EMAIL>"
        test_password = "TestPassword123!"
        test_full_name = "Test User"
        
        # Check if test user already exists
        existing_user = db.query(User).filter(User.email == test_email).first()
        if existing_user:
            print(f"✅ Test user already exists: {test_email}")
            print(f"   Password: {test_password}")
            return existing_user
        
        # Create test user
        hashed_password = get_password_hash(test_password)
        test_user = User(
            email=test_email,
            password_hash=hashed_password,
            full_name=test_full_name,
            email_verified=True,  # Skip email verification for test user
            is_active=True
        )
        
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        print("🎉 Test user created successfully!")
        print(f"   Email: {test_email}")
        print(f"   Password: {test_password}")
        print(f"   User ID: {test_user.id}")
        print(f"   Full Name: {test_full_name}")
        print("\n📝 You can now use these credentials to test the authentication system.")
        
        return test_user
        
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        db.rollback()
        return None
    finally:
        db.close()

if __name__ == "__main__":
    create_test_user()
