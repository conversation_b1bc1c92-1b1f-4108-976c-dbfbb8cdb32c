from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
import time
from typing import Dict, Optional
from collections import defaultdict, deque
import asyncio
from datetime import datetime, timedelta

class RateLimiter:
    """
    Simple in-memory rate limiter using sliding window algorithm.
    For production, consider using Redis for distributed rate limiting.
    """
    
    def __init__(self):
        # Store request timestamps for each client
        self.clients: Dict[str, deque] = defaultdict(deque)
        self.lock = asyncio.Lock()
    
    async def is_allowed(self, client_id: str, max_requests: int, window_seconds: int) -> bool:
        """Check if client is allowed to make a request"""
        async with self.lock:
            now = time.time()
            window_start = now - window_seconds
            
            # Get or create client's request history
            client_requests = self.clients[client_id]
            
            # Remove old requests outside the window
            while client_requests and client_requests[0] < window_start:
                client_requests.popleft()
            
            # Check if client has exceeded the limit
            if len(client_requests) >= max_requests:
                return False
            
            # Add current request
            client_requests.append(now)
            return True
    
    async def cleanup_old_entries(self):
        """Cleanup old entries to prevent memory leaks"""
        async with self.lock:
            current_time = time.time()
            # Remove clients that haven't made requests in the last hour
            cleanup_threshold = current_time - 3600
            
            clients_to_remove = []
            for client_id, requests in self.clients.items():
                if not requests or requests[-1] < cleanup_threshold:
                    clients_to_remove.append(client_id)
            
            for client_id in clients_to_remove:
                del self.clients[client_id]

# Global rate limiter instance
rate_limiter = RateLimiter()

def get_client_id(request: Request) -> str:
    """Get client identifier for rate limiting"""
    # Try to get user ID from auth token first
    user_id = getattr(request.state, 'user_id', None)
    if user_id:
        return f"user:{user_id}"
    
    # Fall back to IP address
    forwarded_for = request.headers.get('X-Forwarded-For')
    if forwarded_for:
        # Get the first IP in case of multiple proxies
        client_ip = forwarded_for.split(',')[0].strip()
    else:
        client_ip = request.client.host if request.client else 'unknown'
    
    return f"ip:{client_ip}"

async def rate_limit_middleware(request: Request, call_next, max_requests: int = 100, window_seconds: int = 60):
    """
    Rate limiting middleware
    
    Args:
        request: FastAPI request object
        call_next: Next middleware/endpoint
        max_requests: Maximum requests allowed in the window
        window_seconds: Time window in seconds
    """
    client_id = get_client_id(request)
    
    # Check if request is allowed
    if not await rate_limiter.is_allowed(client_id, max_requests, window_seconds):
        return JSONResponse(
            status_code=429,
            content={
                "error": "Rate limit exceeded",
                "detail": f"Maximum {max_requests} requests per {window_seconds} seconds allowed",
                "retry_after": window_seconds
            },
            headers={"Retry-After": str(window_seconds)}
        )
    
    # Process the request
    response = await call_next(request)
    
    # Add rate limit headers to response
    remaining_requests = max_requests - len(rate_limiter.clients[client_id])
    response.headers["X-RateLimit-Limit"] = str(max_requests)
    response.headers["X-RateLimit-Remaining"] = str(max(0, remaining_requests))
    response.headers["X-RateLimit-Window"] = str(window_seconds)
    
    return response

# Specific rate limiters for different endpoint types
async def auth_rate_limit(request: Request, call_next):
    """Stricter rate limiting for authentication endpoints"""
    return await rate_limit_middleware(request, call_next, max_requests=10, window_seconds=60)

async def upload_rate_limit(request: Request, call_next):
    """Rate limiting for upload endpoints"""
    return await rate_limit_middleware(request, call_next, max_requests=20, window_seconds=300)  # 20 uploads per 5 minutes

async def api_rate_limit(request: Request, call_next):
    """General API rate limiting"""
    return await rate_limit_middleware(request, call_next, max_requests=100, window_seconds=60)

async def sharing_rate_limit(request: Request, call_next):
    """Rate limiting for sharing endpoints"""
    return await rate_limit_middleware(request, call_next, max_requests=50, window_seconds=60)

# Decorator for applying rate limiting to specific routes
def rate_limit(max_requests: int = 100, window_seconds: int = 60):
    """Decorator for applying rate limiting to FastAPI routes"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # This would need to be implemented differently for route-specific rate limiting
            # For now, we'll use middleware approach
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Background task to cleanup old entries
async def cleanup_rate_limiter():
    """Background task to cleanup old rate limiter entries"""
    while True:
        await asyncio.sleep(300)  # Cleanup every 5 minutes
        await rate_limiter.cleanup_old_entries()

# Rate limiting configuration for different endpoint patterns
RATE_LIMIT_CONFIG = {
    "/auth/": {"max_requests": 10, "window_seconds": 60},
    "/videos/upload": {"max_requests": 20, "window_seconds": 300},
    "/sharing/": {"max_requests": 50, "window_seconds": 60},
    "default": {"max_requests": 100, "window_seconds": 60}
}

def get_rate_limit_for_path(path: str) -> Dict[str, int]:
    """Get rate limit configuration for a specific path"""
    for pattern, config in RATE_LIMIT_CONFIG.items():
        if pattern in path:
            return config
    return RATE_LIMIT_CONFIG["default"]

async def dynamic_rate_limit_middleware(request: Request, call_next):
    """Dynamic rate limiting based on endpoint path"""
    config = get_rate_limit_for_path(request.url.path)
    return await rate_limit_middleware(
        request, 
        call_next, 
        max_requests=config["max_requests"], 
        window_seconds=config["window_seconds"]
    )
