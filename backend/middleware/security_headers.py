from fastapi import Request, Response
from fastapi.responses import JSONResponse
import time

async def security_headers_middleware(request: Request, call_next):
    """Add security headers to all responses"""
    
    response = await call_next(request)
    
    # Security headers
    security_headers = {
        # Prevent clickjacking attacks
        "X-Frame-Options": "DENY",
        
        # Prevent MIME type sniffing
        "X-Content-Type-Options": "nosniff",
        
        # Enable XSS protection
        "X-XSS-Protection": "1; mode=block",
        
        # Referrer policy
        "Referrer-Policy": "strict-origin-when-cross-origin",
        
        # Content Security Policy
        "Content-Security-Policy": (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: blob:; "
            "font-src 'self'; "
            "connect-src 'self'; "
            "media-src 'self' blob:; "
            "object-src 'none'; "
            "base-uri 'self'; "
            "form-action 'self'; "
            "frame-ancestors 'none';"
        ),
        
        # Strict Transport Security (HTTPS only)
        # "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        
        # Permissions Policy (formerly Feature Policy)
        "Permissions-Policy": (
            "camera=(), "
            "microphone=(), "
            "geolocation=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "accelerometer=(), "
            "gyroscope=()"
        ),
        
        # Server identification
        "Server": "tagTok API",
        
        # API version
        "X-API-Version": "1.0",
        
        # Response time
        "X-Response-Time": f"{time.time() - getattr(request.state, 'start_time', time.time()):.3f}s"
    }
    
    # Add all security headers to the response
    for header, value in security_headers.items():
        response.headers[header] = value
    
    return response

async def cors_preflight_middleware(request: Request, call_next):
    """Handle CORS preflight requests"""
    
    if request.method == "OPTIONS":
        # Handle preflight request
        response = JSONResponse(content={})
        
        # CORS headers for preflight
        origin = request.headers.get("origin")
        allowed_origins = [
            "http://localhost:3000",
            "http://127.0.0.1:3000",
            "https://localhost:3000",
            "https://127.0.0.1:3000"
        ]
        
        if origin in allowed_origins:
            response.headers["Access-Control-Allow-Origin"] = origin
        
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH"
        response.headers["Access-Control-Allow-Headers"] = (
            "Content-Type, Authorization, X-Requested-With, Accept, "
            "X-CSRF-Token, X-API-Key, Cache-Control"
        )
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Max-Age"] = "86400"  # 24 hours
        
        return response
    
    return await call_next(request)

async def request_timing_middleware(request: Request, call_next):
    """Add request timing information"""
    start_time = time.time()
    request.state.start_time = start_time
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = f"{process_time:.3f}"
    
    return response

async def api_versioning_middleware(request: Request, call_next):
    """Handle API versioning"""
    
    # Check for API version in headers
    api_version = request.headers.get("X-API-Version", "1.0")
    
    # Validate API version
    supported_versions = ["1.0"]
    if api_version not in supported_versions:
        return JSONResponse(
            status_code=400,
            content={
                "error": "Unsupported API version",
                "supported_versions": supported_versions,
                "requested_version": api_version
            }
        )
    
    # Store version in request state
    request.state.api_version = api_version
    
    response = await call_next(request)
    response.headers["X-API-Version"] = api_version
    
    return response

async def request_id_middleware(request: Request, call_next):
    """Add unique request ID for tracking"""
    import uuid
    
    # Generate or use existing request ID
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    request.state.request_id = request_id
    
    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id
    
    return response

# Content type validation
ALLOWED_CONTENT_TYPES = {
    "application/json",
    "multipart/form-data",
    "application/x-www-form-urlencoded",
    "text/plain",
    "video/mp4",
    "video/avi",
    "video/mov",
    "video/wmv",
    "video/flv",
    "video/webm"
}

async def content_type_validation_middleware(request: Request, call_next):
    """Validate content types for security"""
    
    if request.method in ["POST", "PUT", "PATCH"]:
        content_type = request.headers.get("content-type", "").split(";")[0].strip().lower()
        
        if content_type and content_type not in ALLOWED_CONTENT_TYPES:
            return JSONResponse(
                status_code=415,
                content={
                    "error": "Unsupported Media Type",
                    "detail": f"Content type '{content_type}' is not allowed",
                    "allowed_types": list(ALLOWED_CONTENT_TYPES)
                }
            )
    
    return await call_next(request)

# Combined security middleware
async def combined_security_middleware(request: Request, call_next):
    """Combined security middleware for better performance"""
    
    # Request timing
    start_time = time.time()
    request.state.start_time = start_time
    
    # Request ID
    import uuid
    request_id = request.headers.get("X-Request-ID", str(uuid.uuid4()))
    request.state.request_id = request_id
    
    # API versioning
    api_version = request.headers.get("X-API-Version", "1.0")
    supported_versions = ["1.0"]
    if api_version not in supported_versions:
        return JSONResponse(
            status_code=400,
            content={
                "error": "Unsupported API version",
                "supported_versions": supported_versions,
                "requested_version": api_version
            }
        )
    request.state.api_version = api_version
    
    # Content type validation
    if request.method in ["POST", "PUT", "PATCH"]:
        content_type = request.headers.get("content-type", "").split(";")[0].strip().lower()
        if content_type and content_type not in ALLOWED_CONTENT_TYPES:
            return JSONResponse(
                status_code=415,
                content={
                    "error": "Unsupported Media Type",
                    "detail": f"Content type '{content_type}' is not allowed"
                }
            )
    
    # CORS preflight
    if request.method == "OPTIONS":
        response = JSONResponse(content={})
        origin = request.headers.get("origin")
        allowed_origins = [
            "http://localhost:3000",
            "http://127.0.0.1:3000",
            "https://localhost:3000",
            "https://127.0.0.1:3000"
        ]
        
        if origin in allowed_origins:
            response.headers["Access-Control-Allow-Origin"] = origin
        
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH"
        response.headers["Access-Control-Allow-Headers"] = (
            "Content-Type, Authorization, X-Requested-With, Accept, "
            "X-CSRF-Token, X-API-Key, Cache-Control"
        )
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Max-Age"] = "86400"
        
        return response
    
    # Process request
    response = await call_next(request)
    
    # Add security headers
    process_time = time.time() - start_time
    
    security_headers = {
        "X-Frame-Options": "DENY",
        "X-Content-Type-Options": "nosniff",
        "X-XSS-Protection": "1; mode=block",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Content-Security-Policy": (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: blob:; "
            "connect-src 'self'; "
            "media-src 'self' blob:; "
            "object-src 'none';"
        ),
        "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
        "Server": "tagTok API",
        "X-API-Version": api_version,
        "X-Request-ID": request_id,
        "X-Process-Time": f"{process_time:.3f}",
        "X-Response-Time": f"{process_time:.3f}s"
    }
    
    for header, value in security_headers.items():
        response.headers[header] = value
    
    return response
